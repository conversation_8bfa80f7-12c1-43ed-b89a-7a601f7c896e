// SISOUL JOIAS - MÓDULO DE CLIENTES
// Gestão completa de clientes

window.ClientesModule = (function() {
    'use strict';

    const MODAL_ID = 'modalClientes';
    let isInitialized = false;

    // Criar modal dinamicamente
    function createModal() {
        const modalHTML = `
            <div id="${MODAL_ID}" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>👥 Novo Cliente</h3>
                        <span class="close" onclick="ClientesModule.fecharModal()">&times;</span>
                    </div>
                    <div class="form-section">
                        <h4>👤 Dados Pessoais</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Nome Completo *</label>
                                <input type="text" class="form-input" id="clienteNomeCompleto" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">WhatsApp *</label>
                                <input type="text" class="form-input" id="clienteWhatsAppCompleto" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-input" id="clienteEmailCompleto">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Data de Nascimento</label>
                                <input type="date" class="form-input" id="clienteDataNascimento">
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <h4>📍 Endereço</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">CEP</label>
                                <input type="text" class="form-input" id="clienteCep" placeholder="00000-000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Cidade</label>
                                <input type="text" class="form-input" id="clienteCidade">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Endereço</label>
                                <input type="text" class="form-input" id="clienteEndereco">
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <h4>💍 Preferências de Tamanhos</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Tamanho de Anel</label>
                                <input type="text" class="form-input" id="clienteTamanhoAnel" placeholder="Ex: 18">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Tamanho de Corrente (cm)</label>
                                <input type="text" class="form-input" id="clienteTamanhoCorrente" placeholder="Ex: 45, 50, 60">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Tamanho de Pulseira (cm)</label>
                                <input type="text" class="form-input" id="clienteTamanhoPulseira" placeholder="Ex: 18, 20, 22">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Material Preferido</label>
                                <select class="form-input" id="clienteMaterialPreferido">
                                    <option value="">Selecione</option>
                                    <option value="prata925">Prata 925</option>
                                    <option value="prata950">Prata 950</option>
                                    <option value="ouro18k">Ouro 18k</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Observações</label>
                                <textarea class="form-input" id="clienteObservacoes" rows="3" placeholder="Observações sobre o cliente..."></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <div style="text-align: right;">
                            <button type="button" class="btn" onclick="ClientesModule.fecharModal()">Cancelar</button>
                            <button type="button" class="btn success" onclick="ClientesModule.salvarCliente()">👥 Salvar Cliente</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const container = document.getElementById('modalsContainer') || document.body;
        container.insertAdjacentHTML('beforeend', modalHTML);
        console.log('👥 Modal de clientes criado');
    }

    // Abrir modal de novo cliente
    function novoCliente() {
        console.log('👥 Abrindo modal de novo cliente...');

        let modal = document.getElementById(MODAL_ID);
        if (!modal) {
            createModal();
            modal = document.getElementById(MODAL_ID);
        }

        if (modal) {
            modal.style.display = 'block';
            console.log('✅ Modal de cliente aberto');
        } else {
            console.error('❌ Erro ao criar/abrir modal');
            alert('❌ Erro ao abrir modal de cliente');
        }
    }

    // Fechar modal
    function fecharModal() {
        const modal = document.getElementById(MODAL_ID);
        if (modal) {
            modal.style.display = 'none';
            limparFormulario();
            console.log('🔒 Modal de cliente fechado');
        }
    }

    // Limpar formulário
    function limparFormulario() {
        const campos = [
            'clienteNomeCompleto', 'clienteWhatsAppCompleto', 'clienteEmailCompleto',
            'clienteDataNascimento', 'clienteCep', 'clienteCidade', 'clienteEndereco',
            'clienteTamanhoAnel', 'clienteTamanhoCorrente', 'clienteTamanhoPulseira',
            'clienteMaterialPreferido', 'clienteObservacoes'
        ];
        campos.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) {
                elemento.value = '';
            }
        });
    }

    // Salvar cliente
    function salvarCliente() {
        console.log('👥 Salvando cliente...');

        // Capturar dados
        const dados = {
            nomeCompleto: document.getElementById('clienteNomeCompleto')?.value?.trim(),
            whatsapp: document.getElementById('clienteWhatsAppCompleto')?.value?.trim(),
            email: document.getElementById('clienteEmailCompleto')?.value?.trim(),
            dataNascimento: document.getElementById('clienteDataNascimento')?.value,
            cep: document.getElementById('clienteCep')?.value?.trim(),
            cidade: document.getElementById('clienteCidade')?.value?.trim(),
            endereco: document.getElementById('clienteEndereco')?.value?.trim(),
            tamanhoAnel: document.getElementById('clienteTamanhoAnel')?.value?.trim(),
            tamanhoCorrente: document.getElementById('clienteTamanhoCorrente')?.value?.trim(),
            tamanhoPulseira: document.getElementById('clienteTamanhoPulseira')?.value?.trim(),
            materialPreferido: document.getElementById('clienteMaterialPreferido')?.value,
            observacoes: document.getElementById('clienteObservacoes')?.value?.trim()
        };

        // Validar campos obrigatórios
        if (!dados.nomeCompleto || !dados.whatsapp) {
            alert('❌ Por favor, preencha pelo menos o nome e WhatsApp!');
            return;
        }

        // Criar objeto do cliente
        const cliente = {
            ...dados,
            dataCadastro: new Date().toLocaleDateString('pt-BR'),
            horaCadastro: new Date().toLocaleTimeString('pt-BR'),
            status: 'Ativo'
        };

        // Salvar usando o módulo de storage
        if (window.SisoulStorage) {
            const clienteSalvo = SisoulStorage.saveItem('clientes', cliente);
            console.log('✅ Cliente salvo:', clienteSalvo);

            // Cadastrar aniversário automaticamente se tiver data de nascimento
            if (dados.dataNascimento) {
                cadastrarAniversarioAutomatico(clienteSalvo);
            }
        }

        // Mostrar confirmação
        let mensagem = `✅ CLIENTE SALVO COM SUCESSO!\n\n` +
                      `👤 Nome: ${dados.nomeCompleto}\n` +
                      `📱 WhatsApp: ${dados.whatsapp}\n` +
                      `📅 Data: ${cliente.dataCadastro}`;

        if (dados.dataNascimento) {
            mensagem += `\n🎂 Aniversário cadastrado automaticamente!`;
        }

        alert(mensagem);

        // Fechar modal
        fecharModal();

        // Atualizar lista
        listarClientes();

        // Atualizar estatísticas
        atualizarEstatisticas();
    }

    // Listar clientes
    function listarClientes() {
        console.log('👥 Carregando lista de clientes...');

        const container = document.getElementById('clientesList');
        if (!container) {
            console.log('❌ Container de lista não encontrado');
            return;
        }

        // Obter clientes do storage
        let clientes = [];
        if (window.SisoulStorage) {
            clientes = SisoulStorage.getCollection('clientes');
        }

        if (clientes.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h4>👥 Sistema de Clientes</h4>
                    <p>Nenhum cliente cadastrado ainda.</p>
                    <p>Clique em "➕ Novo Cliente" para começar!</p>
                    <br>
                    <button class="btn success" onclick="ClientesModule.novoCliente()" style="font-size: 18px; padding: 15px 30px;">
                        ➕ Cadastrar Primeiro Cliente
                    </button>
                </div>
            `;
            return;
        }

        // Gerar HTML da lista
        let html = '';
        clientes.forEach(cliente => {
            const idade = cliente.dataNascimento ? calcularIdade(cliente.dataNascimento) : 'N/A';

            html += `
                <div class="item-card">
                    <div class="item-header">
                        <div class="item-title">👤 ${cliente.nomeCompleto}</div>
                        <div>
                            <button class="btn" onclick="ClientesModule.editarCliente('${cliente.id}')">✏️ Editar</button>
                            <button class="btn danger" onclick="ClientesModule.excluirCliente('${cliente.id}')">🗑️ Excluir</button>
                        </div>
                    </div>
                    <div class="item-info">
                        <div class="info-item">
                            <span class="info-label">WhatsApp</span>
                            <span class="info-value">📱 ${cliente.whatsapp}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Email</span>
                            <span class="info-value">📧 ${cliente.email || 'Não informado'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Idade</span>
                            <span class="info-value">🎂 ${idade}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Cidade</span>
                            <span class="info-value">📍 ${cliente.cidade || 'Não informado'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tamanho Anel</span>
                            <span class="info-value">💍 ${cliente.tamanhoAnel || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tamanho Corrente</span>
                            <span class="info-value">📿 ${cliente.tamanhoCorrente || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tamanho Pulseira</span>
                            <span class="info-value">⌚ ${cliente.tamanhoPulseira || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Material Preferido</span>
                            <span class="info-value">✨ ${getMaterialLabel(cliente.materialPreferido)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Data Cadastro</span>
                            <span class="info-value">📅 ${cliente.dataCadastro}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Status</span>
                            <span class="info-value">📊 ${cliente.status}</span>
                        </div>
                    </div>
                    ${cliente.observacoes ? `
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                            <strong>📝 Observações:</strong> ${cliente.observacoes}
                        </div>
                    ` : ''}
                </div>
            `;
        });

        container.innerHTML = html;
        console.log(`✅ Lista carregada com ${clientes.length} clientes`);
    }

    // Cadastrar aniversário automaticamente
    function cadastrarAniversarioAutomatico(cliente) {
        console.log('🎂 Cadastrando aniversário automático para:', cliente.nomeCompleto);

        if (!cliente.dataNascimento || !window.SisoulStorage) {
            console.log('❌ Data de nascimento não informada ou storage não disponível');
            return;
        }

        try {
            // Verificar se já existe um aniversário cadastrado para este cliente
            const datasExistentes = SisoulStorage.getCollection('datas') || [];
            const aniversarioExistente = datasExistentes.find(data =>
                data.clienteId === cliente.id && data.tipoData === 'aniversario'
            );

            if (aniversarioExistente) {
                console.log('🎂 Aniversário já cadastrado para este cliente');
                return;
            }

            // Converter data de nascimento para formato de aniversário
            // CORREÇÃO: Evitar problemas de fuso horário ao processar data do input
            const dataPartes = cliente.dataNascimento.split('-'); // YYYY-MM-DD
            const anoNascimento = parseInt(dataPartes[0]);
            const mesNascimento = parseInt(dataPartes[1]) - 1; // JavaScript usa 0-11 para meses
            const diaNascimento = parseInt(dataPartes[2]);

            console.log(`🎂 Processando nascimento: ${cliente.dataNascimento} -> Dia: ${diaNascimento}, Mês: ${mesNascimento + 1}`);

            const hoje = new Date();

            // Criar data do próximo aniversário (mesmo dia e mês, ano atual ou próximo)
            let proximoAniversario = new Date(hoje.getFullYear(), mesNascimento, diaNascimento);

            // Se o aniversário já passou este ano, usar o próximo ano
            if (proximoAniversario < hoje) {
                proximoAniversario.setFullYear(hoje.getFullYear() + 1);
            }

            // Calcular dias restantes corretamente
            // Zerar as horas para comparar apenas as datas
            const hojeSemHora = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
            const aniversarioSemHora = new Date(proximoAniversario.getFullYear(), proximoAniversario.getMonth(), proximoAniversario.getDate());

            const diasRestantes = Math.round((aniversarioSemHora - hojeSemHora) / (1000 * 60 * 60 * 24));

            // Criar objeto da data especial
            const aniversario = {
                clienteId: cliente.id,
                clienteNome: cliente.nomeCompleto,
                tipoData: 'aniversario',
                dataEspecial: cliente.dataNascimento, // Data original de nascimento
                repetirAnual: true,
                tituloData: `Aniversário de ${cliente.nomeCompleto}`,
                diasLembrete: 15, // Lembrar 15 dias antes
                produtosSugeridos: 'Joias personalizadas, anéis, colares, brincos',
                observacoes: 'Aniversário cadastrado automaticamente',
                dataFormatada: `${diaNascimento.toString().padStart(2, '0')}/${(mesNascimento + 1).toString().padStart(2, '0')}/${anoNascimento}`,
                proximaOcorrencia: `${diaNascimento.toString().padStart(2, '0')}/${(mesNascimento + 1).toString().padStart(2, '0')}/${proximoAniversario.getFullYear()}`,
                diasRestantes: diasRestantes,
                dataCadastro: new Date().toLocaleDateString('pt-BR'),
                horaCadastro: new Date().toLocaleTimeString('pt-BR'),
                status: diasRestantes <= 15 ? 'Próximo' : 'Agendado',
                origem: 'automatico' // Marcar como cadastro automático
            };

            // Salvar aniversário
            const aniversarioSalvo = SisoulStorage.saveItem('datas', aniversario);
            console.log('✅ Aniversário cadastrado automaticamente:', aniversarioSalvo);

        } catch (error) {
            console.error('❌ Erro ao cadastrar aniversário automático:', error);
        }
    }

    // Atualizar aniversário automático quando data de nascimento for alterada
    function atualizarAniversarioAutomatico(cliente, dataAnterior) {
        console.log('🎂 Atualizando aniversário automático para:', cliente.nomeCompleto);

        if (!cliente.dataNascimento || !window.SisoulStorage) {
            console.log('❌ Data de nascimento não informada ou storage não disponível');
            return;
        }

        try {
            // Buscar aniversário existente
            const datasExistentes = SisoulStorage.getCollection('datas') || [];
            const aniversarioExistente = datasExistentes.find(data =>
                data.clienteId === cliente.id && data.tipoData === 'aniversario'
            );

            if (aniversarioExistente) {
                // Atualizar aniversário existente
                // CORREÇÃO: Evitar problemas de fuso horário
                const dataPartes = cliente.dataNascimento.split('-'); // YYYY-MM-DD
                const anoNascimento = parseInt(dataPartes[0]);
                const mesNascimento = parseInt(dataPartes[1]) - 1; // JavaScript usa 0-11 para meses
                const diaNascimento = parseInt(dataPartes[2]);

                console.log(`🎂 Atualizando nascimento: ${cliente.dataNascimento} -> Dia: ${diaNascimento}, Mês: ${mesNascimento + 1}`);

                const hoje = new Date();

                // Criar data do próximo aniversário
                let proximoAniversario = new Date(hoje.getFullYear(), mesNascimento, diaNascimento);
                if (proximoAniversario < hoje) {
                    proximoAniversario.setFullYear(hoje.getFullYear() + 1);
                }

                // Calcular dias restantes corretamente
                const hojeSemHora = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
                const aniversarioSemHora = new Date(proximoAniversario.getFullYear(), proximoAniversario.getMonth(), proximoAniversario.getDate());

                const diasRestantes = Math.round((aniversarioSemHora - hojeSemHora) / (1000 * 60 * 60 * 24));

                // Atualizar dados do aniversário
                const aniversarioAtualizado = {
                    ...aniversarioExistente,
                    dataEspecial: cliente.dataNascimento,
                    dataFormatada: `${diaNascimento.toString().padStart(2, '0')}/${(mesNascimento + 1).toString().padStart(2, '0')}/${anoNascimento}`,
                    proximaOcorrencia: `${diaNascimento.toString().padStart(2, '0')}/${(mesNascimento + 1).toString().padStart(2, '0')}/${proximoAniversario.getFullYear()}`,
                    diasRestantes: diasRestantes,
                    status: diasRestantes <= 15 ? 'Próximo' : 'Agendado',
                    dataAtualizacao: new Date().toLocaleDateString('pt-BR'),
                    horaAtualizacao: new Date().toLocaleTimeString('pt-BR')
                };

                SisoulStorage.saveItem('datas', aniversarioAtualizado);
                console.log('✅ Aniversário atualizado automaticamente');
            } else {
                // Se não existe, criar novo
                cadastrarAniversarioAutomatico(cliente);
            }

        } catch (error) {
            console.error('❌ Erro ao atualizar aniversário automático:', error);
        }
    }

    // Utilitários
    function calcularIdade(dataNascimento) {
        const hoje = new Date();
        const nascimento = new Date(dataNascimento);
        let idade = hoje.getFullYear() - nascimento.getFullYear();
        const mes = hoje.getMonth() - nascimento.getMonth();

        if (mes < 0 || (mes === 0 && hoje.getDate() < nascimento.getDate())) {
            idade--;
        }

        return idade + ' anos';
    }

    function getMaterialLabel(material) {
        const materiais = {
            'prata925': 'Prata 925',
            'prata950': 'Prata 950',
            'ouro18k': 'Ouro 18k'
        };
        return materiais[material] || 'Não informado';
    }

    // Editar cliente
    function editarCliente(id) {
        if (window.SisoulStorage) {
            const cliente = SisoulStorage.getItem('clientes', id);
            if (!cliente) {
                alert('❌ Cliente não encontrado!');
                return;
            }

            // Abrir modal de edição
            let modal = document.getElementById(MODAL_ID);
            if (!modal) {
                createModal();
                modal = document.getElementById(MODAL_ID);
            }

            // Preencher formulário com dados do cliente
            preencherFormularioEdicao(cliente);

            // Alterar título do modal
            const modalHeader = modal.querySelector('.modal-header h3');
            if (modalHeader) {
                modalHeader.textContent = '✏️ Editar Cliente';
            }

            // Alterar botão de salvar
            const saveButton = modal.querySelector('.btn.success');
            if (saveButton) {
                saveButton.textContent = '✏️ Atualizar Cliente';
                saveButton.onclick = () => atualizarCliente(id);
            }

            modal.style.display = 'block';
            console.log('✅ Modal de edição aberto para cliente:', id);
        }
    }

    // Preencher formulário para edição
    function preencherFormularioEdicao(cliente) {
        const campos = {
            'clienteNomeCompleto': cliente.nomeCompleto,
            'clienteWhatsAppCompleto': cliente.whatsapp,
            'clienteEmailCompleto': cliente.email,
            'clienteDataNascimento': cliente.dataNascimento,
            'clienteCep': cliente.cep,
            'clienteEndereco': cliente.endereco,
            'clienteCidade': cliente.cidade,
            'clienteTamanhoAnel': cliente.tamanhoAnel,
            'clienteTamanhoCorrente': cliente.tamanhoCorrente,
            'clienteTamanhoPulseira': cliente.tamanhoPulseira,
            'clienteMaterialPreferido': cliente.materialPreferido,
            'clienteObservacoes': cliente.observacoes
        };

        Object.keys(campos).forEach(campoId => {
            const elemento = document.getElementById(campoId);
            if (elemento && campos[campoId] !== undefined) {
                elemento.value = campos[campoId];
            }
        });
    }

    // Atualizar cliente existente
    function atualizarCliente(id) {
        console.log('✏️ Atualizando cliente...');

        // Capturar dados (usando IDs corretos)
        const dados = {
            nomeCompleto: document.getElementById('clienteNomeCompleto')?.value?.trim(),
            whatsapp: document.getElementById('clienteWhatsAppCompleto')?.value?.trim(),
            email: document.getElementById('clienteEmailCompleto')?.value?.trim(),
            dataNascimento: document.getElementById('clienteDataNascimento')?.value,
            cep: document.getElementById('clienteCep')?.value?.trim(),
            endereco: document.getElementById('clienteEndereco')?.value?.trim(),
            cidade: document.getElementById('clienteCidade')?.value?.trim(),
            tamanhoAnel: document.getElementById('clienteTamanhoAnel')?.value?.trim(),
            tamanhoCorrente: document.getElementById('clienteTamanhoCorrente')?.value?.trim(),
            tamanhoPulseira: document.getElementById('clienteTamanhoPulseira')?.value?.trim(),
            materialPreferido: document.getElementById('clienteMaterialPreferido')?.value,
            observacoes: document.getElementById('clienteObservacoes')?.value?.trim()
        };

        // Validar campos obrigatórios
        if (!dados.nomeCompleto || !dados.whatsapp) {
            alert('❌ Por favor, preencha pelo menos o nome e WhatsApp!');
            return;
        }

        // Obter cliente existente para manter dados originais
        const clienteExistente = SisoulStorage.getItem('clientes', id);

        // Criar objeto do cliente atualizado
        const clienteAtualizado = {
            ...dados,
            id: id, // Manter ID original
            idade: dados.dataNascimento ? calcularIdade(dados.dataNascimento) : null,
            dataCadastro: clienteExistente.dataCadastro, // Manter data original
            horaCadastro: clienteExistente.horaCadastro, // Manter hora original
            dataAtualizacao: new Date().toLocaleDateString('pt-BR'),
            horaAtualizacao: new Date().toLocaleTimeString('pt-BR')
        };

        // Salvar usando o módulo de storage
        if (window.SisoulStorage) {
            const clienteSalvo = SisoulStorage.saveItem('clientes', clienteAtualizado);
            console.log('✅ Cliente atualizado:', clienteSalvo);

            // Verificar se a data de nascimento foi alterada e atualizar aniversário
            if (dados.dataNascimento && dados.dataNascimento !== clienteExistente.dataNascimento) {
                atualizarAniversarioAutomatico(clienteSalvo, clienteExistente.dataNascimento);
            } else if (dados.dataNascimento && !clienteExistente.dataNascimento) {
                // Se não tinha data antes e agora tem, cadastrar aniversário
                cadastrarAniversarioAutomatico(clienteSalvo);
            }
        }

        // Mostrar confirmação
        const mensagem = `✅ CLIENTE ATUALIZADO COM SUCESSO!\n\n` +
                        `👤 Nome: ${dados.nomeCompleto}\n` +
                        `📱 WhatsApp: ${dados.whatsapp}\n` +
                        `🏠 Cidade: ${dados.cidade || 'Não informado'}\n` +
                        `📅 Atualizado em: ${clienteAtualizado.dataAtualizacao}`;

        alert(mensagem);

        // Resetar modal para modo de criação
        resetarModalParaCriacao();

        // Fechar modal
        fecharModal();

        // Atualizar lista
        listarClientes();

        // Atualizar estatísticas
        atualizarEstatisticas();
    }

    // Resetar modal para modo de criação
    function resetarModalParaCriacao() {
        const modal = document.getElementById(MODAL_ID);
        if (modal) {
            // Restaurar título original
            const modalHeader = modal.querySelector('.modal-header h3');
            if (modalHeader) {
                modalHeader.textContent = '👤 Novo Cliente';
            }

            // Restaurar botão de salvar
            const saveButton = modal.querySelector('.btn.success');
            if (saveButton) {
                saveButton.textContent = '👤 Salvar Cliente';
                saveButton.onclick = () => ClientesModule.salvarCliente();
            }
        }
    }

    // Excluir cliente
    function excluirCliente(id) {
        if (confirm('🗑️ Deseja realmente excluir este cliente?\n\nEsta ação não pode ser desfeita!')) {
            if (window.SisoulStorage) {
                SisoulStorage.removeItem('clientes', id);
            }
            listarClientes();
            atualizarEstatisticas();
            alert('✅ Cliente excluído com sucesso!');
        }
    }

    // Atualizar estatísticas
    function atualizarEstatisticas() {
        if (window.SisoulStorage && window.SisoulCore) {
            const clientes = SisoulStorage.getCollection('clientes');
            SisoulCore.updateStatistics({
                totalClientes: clientes.length
            });
        }
    }

    // Inicialização do módulo
    function init() {
        console.log('👥 Inicializando módulo de Clientes...');

        // Escutar eventos do core
        if (window.SisoulCore) {
            SisoulCore.events.on('tabChanged', function(event) {
                if (event.detail.tabName === 'clientes') {
                    listarClientes();
                }
            });
        }

        isInitialized = true;
        console.log('✅ Módulo de Clientes inicializado');
    }

    // Cadastrar aniversários de todos os clientes existentes (função utilitária)
    function cadastrarAniversariosExistentes() {
        console.log('🎂 Cadastrando aniversários de clientes existentes...');

        if (!window.SisoulStorage) {
            alert('❌ Sistema de storage não disponível!');
            return;
        }

        const clientes = SisoulStorage.getCollection('clientes') || [];
        let cadastrados = 0;
        let jaExistiam = 0;

        clientes.forEach(cliente => {
            if (cliente.dataNascimento) {
                // Verificar se já existe aniversário
                const datasExistentes = SisoulStorage.getCollection('datas') || [];
                const aniversarioExistente = datasExistentes.find(data =>
                    data.clienteId === cliente.id && data.tipoData === 'aniversario'
                );

                if (!aniversarioExistente) {
                    cadastrarAniversarioAutomatico(cliente);
                    cadastrados++;
                } else {
                    jaExistiam++;
                }
            }
        });

        const mensagem = `🎂 CADASTRO DE ANIVERSÁRIOS CONCLUÍDO!\n\n` +
                        `✅ Novos aniversários cadastrados: ${cadastrados}\n` +
                        `📅 Aniversários já existentes: ${jaExistiam}\n` +
                        `👥 Total de clientes verificados: ${clientes.length}`;

        alert(mensagem);
        console.log(`✅ Processo concluído: ${cadastrados} novos, ${jaExistiam} existentes`);
    }

    // Testar processamento de data de nascimento (função de debug)
    function testarProcessamentoData() {
        const testeDatas = [
            '1990-06-15', // 15 de junho
            '1985-12-25', // 25 de dezembro
            '2000-01-01', // 1 de janeiro
            '1995-02-29', // 29 de fevereiro (ano bissexto)
            '1993-07-04'  // 4 de julho
        ];

        let resultado = '🧪 TESTE DE PROCESSAMENTO DE DATAS:\n\n';

        testeDatas.forEach((dataInput, index) => {
            resultado += `${index + 1}. Input: ${dataInput}\n`;

            // Método antigo (problemático)
            const dataAntiga = new Date(dataInput);
            const diaAntigo = dataAntiga.getDate();
            const mesAntigo = dataAntiga.getMonth() + 1;

            // Método novo (correto)
            const dataPartes = dataInput.split('-');
            const diaNovo = parseInt(dataPartes[2]);
            const mesNovo = parseInt(dataPartes[1]);

            resultado += `   Método antigo: ${diaAntigo}/${mesAntigo}\n`;
            resultado += `   Método novo: ${diaNovo}/${mesNovo}\n`;
            resultado += `   ${diaAntigo === diaNovo && mesAntigo === mesNovo ? '✅ Igual' : '❌ Diferente'}\n\n`;

            console.log(`Teste ${index + 1}:`, {
                input: dataInput,
                antigo: { dia: diaAntigo, mes: mesAntigo },
                novo: { dia: diaNovo, mes: mesNovo },
                igual: diaAntigo === diaNovo && mesAntigo === mesNovo
            });
        });

        resultado += '💡 O método novo garante que o dia do aniversário\n';
        resultado += 'seja exatamente igual ao dia de nascimento!';

        alert(resultado);
    }

    // Testar cálculo do próximo aniversário (função de debug)
    function testarProximoAniversario() {
        const testeDatas = [
            '1990-06-15', // 15 de junho
            '1985-12-25', // 25 de dezembro
            '2000-01-01', // 1 de janeiro
            '1993-07-04'  // 4 de julho
        ];

        let resultado = '🧪 TESTE DO PRÓXIMO ANIVERSÁRIO:\n\n';
        const hoje = new Date();
        resultado += `📅 Data atual: ${hoje.toLocaleDateString('pt-BR')}\n\n`;

        testeDatas.forEach((dataInput, index) => {
            resultado += `${index + 1}. Nascimento: ${dataInput}\n`;

            // Processar data
            const dataPartes = dataInput.split('-');
            const anoNascimento = parseInt(dataPartes[0]);
            const mesNascimento = parseInt(dataPartes[1]) - 1;
            const diaNascimento = parseInt(dataPartes[2]);

            // Criar próximo aniversário
            let proximoAniversario = new Date(hoje.getFullYear(), mesNascimento, diaNascimento);
            if (proximoAniversario < hoje) {
                proximoAniversario.setFullYear(hoje.getFullYear() + 1);
            }

            // Método antigo (problemático)
            const proximaOcorrenciaAntiga = proximoAniversario.toLocaleDateString('pt-BR');

            // Método novo (correto)
            const proximaOcorrenciaNova = `${diaNascimento.toString().padStart(2, '0')}/${(mesNascimento + 1).toString().padStart(2, '0')}/${proximoAniversario.getFullYear()}`;

            resultado += `   Dia/Mês nascimento: ${diaNascimento}/${mesNascimento + 1}\n`;
            resultado += `   Ano próximo aniversário: ${proximoAniversario.getFullYear()}\n`;
            resultado += `   Método antigo: ${proximaOcorrenciaAntiga}\n`;
            resultado += `   Método novo: ${proximaOcorrenciaNova}\n`;
            resultado += `   ${proximaOcorrenciaAntiga === proximaOcorrenciaNova ? '✅ Igual' : '❌ Diferente'}\n\n`;

            console.log(`Teste próximo aniversário ${index + 1}:`, {
                nascimento: dataInput,
                diaOriginal: diaNascimento,
                mesOriginal: mesNascimento + 1,
                anoProximo: proximoAniversario.getFullYear(),
                metodoAntigo: proximaOcorrenciaAntiga,
                metodoNovo: proximaOcorrenciaNova
            });
        });

        resultado += '💡 O método novo garante que o dia do próximo\n';
        resultado += 'aniversário seja exatamente igual ao nascimento!';

        alert(resultado);
    }

    // API pública do módulo
    return {
        init,
        novoCliente,
        fecharModal,
        salvarCliente,
        atualizarCliente,
        listarClientes,
        editarCliente,
        excluirCliente,
        cadastrarAniversariosExistentes,
        testarProcessamentoData,
        testarProximoAniversario
    };
})();

console.log('👥 Clientes.js carregado com sucesso!');
