// SISOUL JOIAS - MÓDULO DE ORÇAMENTOS
// Sistema modular e funcional para gestão de orçamentos

window.OrcamentosModule = (function() {
    'use strict';

    const MODAL_ID = 'modalOrcamentos';
    let isInitialized = false;

    // Teste básico para verificar se o módulo funciona
    function testeBasico() {
        // Verificar bibliotecas
        let pdfStatus = '❌ Não carregada';
        if (window.jsPDF || window.jspdf || window.sisoulPDFAvailable) {
            pdfStatus = '✅ Carregada';
        }

        const mensagem = `🧪 TESTE DO MÓDULO DE ORÇAMENTOS\n\n` +
                        `✅ JavaScript carregado\n` +
                        `✅ Função executada\n` +
                        `✅ Sistema modular operacional\n` +
                        `📄 Biblioteca PDF: ${pdfStatus}\n\n` +
                        `${pdfStatus.includes('❌') ? '💡 Se PDF não funcionar, será usado método alternativo' : '🎉 PDF pronto para uso!'}`;

        alert(mensagem);
    }

    // Criar modal dinamicamente
    function createModal() {
        const modalHTML = `
            <div id="${MODAL_ID}" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>🧾 Novo Orçamento</h3>
                        <span class="close" onclick="OrcamentosModule.fecharModal()">&times;</span>
                    </div>
                    <div class="form-section">
                        <h4>👥 Dados do Cliente</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Nome Completo *</label>
                                <input type="text" class="form-input" id="clienteNome" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">WhatsApp *</label>
                                <input type="text" class="form-input" id="clienteWhatsapp" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-input" id="clienteEmail">
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <h4>💎 Especificações do Produto</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Nome da Joia *</label>
                                <input type="text" class="form-input" id="produtoNome" placeholder="Ex: Anel de Compromisso, Aliança, Corrente..." required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Material *</label>
                                <select class="form-input" id="produtoMaterial" required>
                                    <option value="">Selecione o material</option>
                                    <option value="ouro18k">Ouro 18k</option>
                                    <option value="prata925">Prata 925</option>
                                    <option value="prata950">Prata 950</option>
                                    <option value="aco">Aço Inoxidável</option>
                                    <option value="folheado">Folheado a Ouro</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Possui Pedras?</label>
                                <select class="form-input" id="produtoPedras" onchange="OrcamentosModule.togglePedrasDetalhes()">
                                    <option value="nao">Não possui pedras</option>
                                    <option value="sim">Sim, possui pedras</option>
                                </select>
                            </div>
                            <div class="form-group" id="pedrasDetalhes" style="display: none;">
                                <label class="form-label">Tipo de Pedras</label>
                                <select class="form-input" id="produtoTipoPedras">
                                    <option value="">Selecione o tipo</option>
                                    <option value="sinteticas">Pedras Sintéticas (Zircônia)</option>
                                    <option value="naturais">Pedras Naturais</option>
                                    <option value="semipreciosas">Pedras Semipreciosas</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Observações Adicionais</label>
                                <textarea class="form-input" id="produtoObservacoes" rows="2" placeholder="Gravações, acabamentos especiais, etc..."></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Prazo de Entrega *</label>
                                <select class="form-input" id="prazoEntrega" required>
                                    <option value="">Selecione o prazo</option>
                                    <option value="7-10">7 a 10 dias úteis</option>
                                    <option value="10-12">10 a 12 dias úteis</option>
                                    <option value="12-15">12 a 15 dias úteis</option>
                                    <option value="15-20">15 a 20 dias úteis</option>
                                    <option value="20-30">20 a 30 dias úteis</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Valor Total (R$) *</label>
                                <input type="number" class="form-input" id="produtoValor" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <div style="text-align: right;">
                            <button type="button" class="btn" onclick="OrcamentosModule.fecharModal()">Cancelar</button>
                            <button type="button" class="btn success" onclick="OrcamentosModule.salvarOrcamento()">💾 Salvar</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Adicionar modal ao container
        const container = document.getElementById('modalsContainer');
        if (container) {
            container.innerHTML += modalHTML;
            console.log('📋 Modal de orçamentos criado');
        } else {
            // Se não existe container, adicionar ao body
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            console.log('📋 Modal de orçamentos criado no body');
        }
    }

    // Abrir modal de novo orçamento
    function novoOrcamento() {
        console.log('🧾 Abrindo modal de novo orçamento...');

        // Verificar se modal existe, senão criar
        let modal = document.getElementById(MODAL_ID);
        if (!modal) {
            createModal();
            modal = document.getElementById(MODAL_ID);
        }

        if (modal) {
            modal.style.display = 'block';
            console.log('✅ Modal de orçamento aberto');
        } else {
            console.error('❌ Erro ao criar/abrir modal');
            alert('❌ Erro ao abrir modal de orçamento');
        }
    }

    // Fechar modal
    function fecharModal() {
        const modal = document.getElementById(MODAL_ID);
        if (modal) {
            modal.style.display = 'none';
            limparFormulario();
            console.log('🔒 Modal de orçamento fechado');
        }
    }

    // Mostrar/ocultar detalhes das pedras
    function togglePedrasDetalhes() {
        const produtoPedras = document.getElementById('produtoPedras');
        const pedrasDetalhes = document.getElementById('pedrasDetalhes');

        if (produtoPedras && pedrasDetalhes) {
            if (produtoPedras.value === 'sim') {
                pedrasDetalhes.style.display = 'block';
            } else {
                pedrasDetalhes.style.display = 'none';
                // Limpar seleção quando ocultar
                const tipoPedras = document.getElementById('produtoTipoPedras');
                if (tipoPedras) tipoPedras.value = '';
            }
        }
    }

    // Limpar formulário
    function limparFormulario() {
        const campos = [
            'clienteNome', 'clienteWhatsapp', 'clienteEmail',
            'produtoNome', 'produtoMaterial', 'produtoPedras', 'produtoTipoPedras',
            'produtoObservacoes', 'produtoValor', 'prazoEntrega'
        ];
        campos.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) {
                elemento.value = '';
            }
        });

        // Ocultar detalhes das pedras
        const pedrasDetalhes = document.getElementById('pedrasDetalhes');
        if (pedrasDetalhes) {
            pedrasDetalhes.style.display = 'none';
        }
    }

    // Salvar orçamento
    function salvarOrcamento() {
        console.log('💾 Salvando orçamento...');

        // Capturar dados
        const dados = {
            clienteNome: document.getElementById('clienteNome')?.value?.trim(),
            clienteWhatsapp: document.getElementById('clienteWhatsapp')?.value?.trim(),
            clienteEmail: document.getElementById('clienteEmail')?.value?.trim(),
            produtoNome: document.getElementById('produtoNome')?.value?.trim(),
            produtoMaterial: document.getElementById('produtoMaterial')?.value,
            produtoPedras: document.getElementById('produtoPedras')?.value,
            produtoTipoPedras: document.getElementById('produtoTipoPedras')?.value,
            produtoObservacoes: document.getElementById('produtoObservacoes')?.value?.trim(),
            produtoValor: document.getElementById('produtoValor')?.value?.trim(),
            prazoEntrega: document.getElementById('prazoEntrega')?.value
        };

        // Validar campos obrigatórios
        if (!dados.clienteNome || !dados.clienteWhatsapp || !dados.produtoNome || !dados.produtoMaterial || !dados.produtoValor || !dados.prazoEntrega) {
            alert('❌ Por favor, preencha todos os campos obrigatórios!');
            return;
        }

        // Validar pedras se selecionado
        if (dados.produtoPedras === 'sim' && !dados.produtoTipoPedras) {
            alert('❌ Por favor, selecione o tipo de pedras!');
            return;
        }

        // Validar valor
        const valor = parseFloat(dados.produtoValor);
        if (isNaN(valor) || valor <= 0) {
            alert('❌ Por favor, insira um valor válido!');
            return;
        }

        // Gerar descrição completa do produto
        function gerarDescricaoProduto(dados) {
            let descricao = dados.produtoNome;

            // Adicionar material
            const materiais = {
                'ouro18k': 'Ouro 18k',
                'prata925': 'Prata 925',
                'prata950': 'Prata 950',
                'aco': 'Aço Inoxidável',
                'folheado': 'Folheado a Ouro'
            };

            if (dados.produtoMaterial) {
                descricao += ` em ${materiais[dados.produtoMaterial]}`;
            }

            // Adicionar informações sobre pedras
            if (dados.produtoPedras === 'sim' && dados.produtoTipoPedras) {
                const tiposPedras = {
                    'sinteticas': 'com Pedras Sintéticas (Zircônia)',
                    'naturais': 'com Pedras Naturais',
                    'semipreciosas': 'com Pedras Semipreciosas'
                };
                descricao += ` ${tiposPedras[dados.produtoTipoPedras]}`;
            }

            // Adicionar observações se houver
            if (dados.produtoObservacoes) {
                descricao += ` - ${dados.produtoObservacoes}`;
            }

            return descricao;
        }

        // Gerar número sequencial do orçamento
        const numeroOrcamento = gerarNumeroOrcamento();

        // Criar objeto do orçamento
        const orcamento = {
            id: Date.now().toString(),
            numeroOrcamento,
            clienteNome: dados.clienteNome,
            clienteWhatsapp: dados.clienteWhatsapp,
            clienteEmail: dados.clienteEmail,
            // Dados detalhados do produto
            produtoNome: dados.produtoNome,
            produtoMaterial: dados.produtoMaterial,
            produtoPedras: dados.produtoPedras,
            produtoTipoPedras: dados.produtoTipoPedras,
            produtoObservacoes: dados.produtoObservacoes,
            // Descrição completa gerada
            produtoDescricao: gerarDescricaoProduto(dados),
            produtoValor: valor,
            valorFormatado: new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(valor),
            prazoEntrega: dados.prazoEntrega,
            prazoEntregaFormatado: getPrazoEntregaLabel(dados.prazoEntrega),
            dataOrcamento: new Date().toLocaleDateString('pt-BR'),
            horaOrcamento: new Date().toLocaleTimeString('pt-BR'),
            status: 'Pendente'
        };

        // Salvar usando o módulo de storage
        if (window.SisoulStorage) {
            const orcamentoSalvo = SisoulStorage.saveItem('orcamentos', orcamento);
            console.log('✅ Orçamento salvo:', orcamentoSalvo);
        } else {
            console.log('⚠️ Storage não disponível, salvando localmente');
        }

        // Mostrar confirmação
        const mensagem = `✅ ORÇAMENTO SALVO COM SUCESSO!\n\n` +
                        `👤 Cliente: ${dados.clienteNome}\n` +
                        `📱 WhatsApp: ${dados.clienteWhatsapp}\n` +
                        `💎 Produto: ${dados.produtoDescricao}\n` +
                        `💰 Valor: ${orcamento.valorFormatado}\n` +
                        `📅 Data: ${orcamento.dataOrcamento}`;

        alert(mensagem);

        // Fechar modal
        fecharModal();

        // Atualizar lista
        listarOrcamentos();

        // Atualizar estatísticas
        atualizarEstatisticas();
    }

    // Variáveis de paginação
    let currentPage = 1;
    const itemsPerPage = 10;

    // Listar orçamentos com paginação
    function listarOrcamentos(page = 1) {
        console.log('📋 Carregando lista de orçamentos...');

        const container = document.getElementById('orcamentosList');
        if (!container) {
            console.log('❌ Container de lista não encontrado');
            return;
        }

        currentPage = page;

        // Obter orçamentos do storage
        let orcamentos = [];
        if (window.SisoulStorage) {
            orcamentos = SisoulStorage.getCollection('orcamentos');
            // Ordenar por data mais recente primeiro
            orcamentos.sort((a, b) => {
                const dateA = new Date(a.dataOrcamento.split('/').reverse().join('-') + ' ' + a.horaOrcamento);
                const dateB = new Date(b.dataOrcamento.split('/').reverse().join('-') + ' ' + b.horaOrcamento);
                return dateB - dateA;
            });
        }

        if (orcamentos.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h4>🧾 Sistema de Orçamentos Modular</h4>
                    <p>Sistema organizado em módulos independentes.</p>
                    <p>Clique em "➕ Novo Orçamento" para começar!</p>
                    <br>
                    <button class="btn success" onclick="OrcamentosModule.novoOrcamento()" style="font-size: 18px; padding: 15px 30px;">
                        ➕ Criar Primeiro Orçamento
                    </button>
                </div>
            `;
            return;
        }

        // Calcular paginação
        const totalItems = orcamentos.length;
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const orcamentosPagina = orcamentos.slice(startIndex, endIndex);

        // Gerar HTML da lista
        let html = '';

        // Informações de paginação
        html += `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 15px; background: #f8f4ef; border-radius: 8px;">
                <div style="color: #96815d; font-weight: bold;">
                    📊 Mostrando ${startIndex + 1}-${Math.min(endIndex, totalItems)} de ${totalItems} orçamentos
                </div>
                <div style="color: #666;">
                    📅 Ordenados por data (mais recentes primeiro)
                </div>
            </div>
        `;

        orcamentosPagina.forEach(orc => {
            html += `
                <div class="item-card">
                    <div class="item-header">
                        <div class="item-title">Orçamento #${orc.numeroOrcamento || orc.id.substr(-6).toUpperCase()}</div>
                        <div>
                            <button class="btn" onclick="OrcamentosModule.gerarPDF('${orc.id}')">📄 PDF</button>
                            <button class="btn success" onclick="OrcamentosModule.converterParaVenda('${orc.id}')">💰 Converter p/ Venda</button>
                            <button class="btn danger" onclick="OrcamentosModule.excluirOrcamento('${orc.id}')">🗑️ Excluir</button>
                        </div>
                    </div>
                    <div class="item-info">
                        <div class="info-item">
                            <span class="info-label">Cliente</span>
                            <span class="info-value">👤 ${orc.clienteNome}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">WhatsApp</span>
                            <span class="info-value">📱 ${orc.clienteWhatsapp}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Produto</span>
                            <span class="info-value">💎 ${orc.produtoDescricao}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Valor</span>
                            <span class="info-value" style="color: #28a745; font-weight: bold;">💰 ${orc.valorFormatado}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Data</span>
                            <span class="info-value">📅 ${orc.dataOrcamento}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Status</span>
                            <span class="info-value">📊 ${orc.status}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        // Adicionar controles de paginação
        if (totalPages > 1) {
            html += `
                <div style="display: flex; justify-content: center; align-items: center; margin-top: 30px; gap: 10px;">
                    <button class="btn" onclick="OrcamentosModule.listarOrcamentos(${currentPage - 1})"
                            ${currentPage === 1 ? 'disabled style="opacity: 0.5;"' : ''}>
                        ← Anterior
                    </button>

                    <div style="display: flex; gap: 5px;">
            `;

            for (let i = 1; i <= totalPages; i++) {
                const isActive = i === currentPage;
                html += `
                    <button class="btn ${isActive ? 'success' : ''}"
                            onclick="OrcamentosModule.listarOrcamentos(${i})"
                            style="${isActive ? 'background: #96815d; color: white;' : 'background: white; color: #96815d;'} min-width: 40px;">
                        ${i}
                    </button>
                `;
            }

            html += `
                    </div>

                    <button class="btn" onclick="OrcamentosModule.listarOrcamentos(${currentPage + 1})"
                            ${currentPage === totalPages ? 'disabled style="opacity: 0.5;"' : ''}>
                        Próxima →
                    </button>
                </div>
            `;
        }

        container.innerHTML = html;
        console.log(`✅ Lista carregada com ${orcamentosPagina.length} orçamentos (página ${currentPage} de ${totalPages})`);
    }

    // Gerar PDF real
    function gerarPDF(id) {
        // Verificar se a biblioteca está disponível
        if (!window.jsPDF && !window.jspdf && !window.sisoulPDFAvailable) {
            gerarPDFAlternativo(id);
            return;
        }

        if (window.SisoulStorage) {
            const orcamento = SisoulStorage.getItem('orcamentos', id);
            if (!orcamento) {
                alert('❌ Orçamento não encontrado!');
                return;
            }

            try {
                console.log('📄 Criando documento PDF...');

                // Tentar diferentes formas de acessar jsPDF
                let jsPDFClass;
                if (window.jsPDF && window.jsPDF.jsPDF) {
                    jsPDFClass = window.jsPDF.jsPDF;
                } else if (window.jsPDF) {
                    jsPDFClass = window.jsPDF;
                } else if (window.jspdf) {
                    jsPDFClass = window.jspdf.jsPDF;
                }

                if (!jsPDFClass) {
                    throw new Error('jsPDF class not found');
                }

                const doc = new jsPDFClass();

                // Configurações e cores da marca
                const pageWidth = doc.internal.pageSize.width;
                const pageHeight = doc.internal.pageSize.height;
                const margin = 20;
                let yPosition = 25;

                // Cores da Sisoul Joias
                const corPrimaria = [150, 129, 93]; // #96815d
                const corSecundaria = [248, 244, 239]; // #f8f4ef
                const corTexto = [51, 51, 51]; // #333333
                const corDestaque = [40, 167, 69]; // #28a745

                // Cabeçalho com fundo colorido
                doc.setFillColor(...corPrimaria);
                doc.rect(0, 0, pageWidth, 50, 'F');

                // Carregar e adicionar as imagens da Sisoul
                adicionarImagensSisoul(doc, pageWidth, pageHeight, corPrimaria);

                // Título principal (sem símbolos)
                doc.setTextColor(255, 255, 255);
                doc.setFontSize(24);
                doc.setFont(undefined, 'bold');
                doc.text('SISOUL JOIAS', pageWidth / 2, 25, { align: 'center' });

                doc.setFontSize(14);
                doc.setFont(undefined, 'normal');
                doc.text('Joias que tocam a alma', pageWidth / 2, 40, { align: 'center' });

                // Reset cor do texto
                doc.setTextColor(...corTexto);
                yPosition = 65;

                // Título do documento com fundo
                doc.setFillColor(...corSecundaria);
                doc.rect(margin, yPosition - 5, pageWidth - (margin * 2), 20, 'F');

                doc.setFontSize(18);
                doc.setFont(undefined, 'bold');
                doc.setTextColor(...corPrimaria);
                doc.text(`ORÇAMENTO Nº ${orcamento.numeroOrcamento || orcamento.id.substr(-6).toUpperCase()}`, pageWidth / 2, yPosition + 8, { align: 'center' });

                yPosition += 30;

                // Reset cor do texto para seções
                doc.setTextColor(...corTexto);

                // SEÇÃO: DADOS DO CLIENTE
                createSection(doc, 'DADOS DO CLIENTE', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
                yPosition += 20;

                const dadosCliente = [
                    { label: 'Nome Completo:', valor: orcamento.clienteNome },
                    { label: 'WhatsApp:', valor: orcamento.clienteWhatsapp },
                    { label: 'Email:', valor: orcamento.clienteEmail || 'Não informado' },
                    { label: 'Data do Orçamento:', valor: `${orcamento.dataOrcamento} às ${orcamento.horaOrcamento}` }
                ];

                dadosCliente.forEach(item => {
                    doc.setFont(undefined, 'bold');
                    doc.setFontSize(10);
                    doc.text(item.label, margin + 5, yPosition);

                    doc.setFont(undefined, 'normal');
                    doc.text(item.valor, margin + 45, yPosition);
                    yPosition += 8;
                });

                yPosition += 10;

                // SEÇÃO: DADOS DO PRODUTO
                createSection(doc, 'ESPECIFICAÇÕES DO PRODUTO', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
                yPosition += 20;

                // Produto em destaque
                doc.setFillColor(255, 255, 255);
                doc.setDrawColor(...corPrimaria);
                doc.rect(margin + 5, yPosition - 5, pageWidth - (margin * 2) - 10, 25, 'FD');

                doc.setFont(undefined, 'bold');
                doc.setFontSize(14);
                doc.setTextColor(...corPrimaria);
                doc.text(orcamento.produtoDescricao, margin + 10, yPosition + 5);

                doc.setFont(undefined, 'bold');
                doc.setFontSize(16);
                doc.setTextColor(...corDestaque);
                doc.text(orcamento.valorFormatado, pageWidth - margin - 10, yPosition + 5, { align: 'right' });

                yPosition += 35;

                // SEÇÃO: CONDIÇÕES E OBSERVAÇÕES
                createSection(doc, 'CONDIÇÕES COMERCIAIS', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
                yPosition += 20;

                const condicoes = [
                    '✓ Orçamento válido por 15 dias úteis',
                    '✓ Valores sujeitos à variação da cotação do metal',
                    `✓ Prazo de entrega: ${orcamento.prazoEntregaFormatado || 'A definir'}`,
                    '✓ Produto 100% artesanal, pequenas variações são naturais',
                    '✓ Garantia de 6 meses contra defeitos de fabricação',
                    '✓ Acompanha certificado de autenticidade'
                ];

                doc.setFont(undefined, 'normal');
                doc.setFontSize(10);
                doc.setTextColor(...corTexto);

                condicoes.forEach(condicao => {
                    doc.text(condicao, margin + 5, yPosition);
                    yPosition += 7;
                });

                // SEÇÃO: INFORMAÇÕES DE CONTATO
                yPosition += 15;
                createSection(doc, 'INFORMAÇÕES DE CONTATO', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
                yPosition += 20;

                const contatos = [
                    '📱 WhatsApp: Entre em contato para dúvidas',
                    '📧 Email: Envie fotos de referência',
                    '🏪 Atendimento: Segunda a Sexta, 9h às 18h',
                    '💎 Especialidade: Alianças, Anéis de Formatura e Joias Personalizadas'
                ];

                contatos.forEach(contato => {
                    doc.text(contato, margin + 5, yPosition);
                    yPosition += 7;
                });

                // RODAPÉ ESTILIZADO
                const rodapeY = pageHeight - 40;

                // Fundo do rodapé
                doc.setFillColor(...corPrimaria);
                doc.rect(0, rodapeY, pageWidth, 40, 'F');

                // Texto do rodapé
                doc.setTextColor(255, 255, 255);
                doc.setFont(undefined, 'bold');
                doc.setFontSize(12);
                doc.text('SISOUL JOIAS', pageWidth / 2, rodapeY + 8, { align: 'center' });

                doc.setFont(undefined, 'normal');
                doc.setFontSize(9);
                doc.text('Joias que tocam a alma', pageWidth / 2, rodapeY + 16, { align: 'center' });

                // Informações de contato
                doc.setFontSize(8);
                doc.text('WhatsApp: (85) 99868-4795 | Instagram: @sisouljoias', pageWidth / 2, rodapeY + 24, { align: 'center' });

                // CNPJ e data de emissão
                doc.setFontSize(7);
                doc.text(`CNPJ: 40.640.553/0001-23 | Emitido em: ${new Date().toLocaleString('pt-BR')}`, pageWidth / 2, rodapeY + 32, { align: 'center' });

                // Salvar PDF
                const fileName = `Orcamento_Sisoul_${orcamento.id.substr(-6).toUpperCase()}_${orcamento.clienteNome.replace(/\s+/g, '_')}.pdf`;
                doc.save(fileName);

                console.log('✅ PDF gerado com sucesso:', fileName);

                if (window.SisoulUI) {
                    SisoulUI.showToast('📄 PDF gerado com sucesso!', 'success');
                } else {
                    alert('✅ PDF gerado e baixado com sucesso!');
                }

            } catch (error) {
                console.error('❌ Erro ao gerar PDF:', error);
                console.log('🔄 Tentando método alternativo...');
                gerarPDFAlternativo(id);
            }
        }
    }

    // Função auxiliar para criar seções estilizadas no PDF
    function createSection(doc, titulo, yPosition, pageWidth, margin, corPrimaria, corSecundaria) {
        // Fundo da seção
        doc.setFillColor(...corSecundaria);
        doc.rect(margin, yPosition - 3, pageWidth - (margin * 2), 15, 'F');

        // Título da seção
        doc.setFont(undefined, 'bold');
        doc.setFontSize(12);
        doc.setTextColor(...corPrimaria);
        doc.text(titulo, margin + 5, yPosition + 7);

        // Linha decorativa
        doc.setDrawColor(...corPrimaria);
        doc.setLineWidth(0.5);
        doc.line(margin + 5, yPosition + 10, pageWidth - margin - 5, yPosition + 10);
    }

    // Método alternativo para gerar PDF (usando window.print)
    function gerarPDFAlternativo(id) {
        console.log('📄 Gerando PDF alternativo...');

        if (window.SisoulStorage) {
            const orcamento = SisoulStorage.getItem('orcamentos', id);
            if (!orcamento) {
                alert('❌ Orçamento não encontrado!');
                return;
            }

            // Criar janela popup com conteúdo para impressão
            const printWindow = window.open('', '_blank', 'width=800,height=600');

            const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Orçamento Sisoul Joias</title>
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            margin: 0;
                            line-height: 1.6;
                            color: #333;
                        }
                        .header {
                            background: linear-gradient(135deg, #96815d 0%, #b8a082 100%);
                            color: white;
                            text-align: center;
                            padding: 30px 20px;
                            margin-bottom: 30px;
                        }
                        .header h1 {
                            color: white;
                            margin: 0;
                            font-size: 28px;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                        }
                        .header .subtitle {
                            font-size: 14px;
                            margin-top: 5px;
                            opacity: 0.9;
                        }
                        .header .slogan {
                            font-size: 12px;
                            margin-top: 10px;
                            font-style: italic;
                            opacity: 0.8;
                        }
                        .content {
                            padding: 0 30px;
                        }
                        .section {
                            margin-bottom: 30px;
                            background: #f8f4ef;
                            border-radius: 10px;
                            padding: 20px;
                            border-left: 5px solid #96815d;
                        }
                        .section h3 {
                            color: #96815d;
                            margin: 0 0 15px 0;
                            font-size: 18px;
                            border-bottom: 2px solid #96815d;
                            padding-bottom: 8px;
                        }
                        .info-row {
                            margin: 12px 0;
                            display: flex;
                            align-items: center;
                        }
                        .label {
                            font-weight: bold;
                            color: #96815d;
                            min-width: 140px;
                            font-size: 14px;
                        }
                        .value {
                            color: #333;
                            font-size: 14px;
                            flex: 1;
                        }
                        .produto-destaque {
                            background: white;
                            border: 2px solid #96815d;
                            border-radius: 10px;
                            padding: 30px;
                            margin: 30px 0;
                            page-break-inside: avoid;
                            overflow: hidden;
                        }
                        .produto-section {
                            margin-bottom: 25px;
                            padding-bottom: 20px;
                            border-bottom: 2px solid #e0e0e0;
                        }
                        .produto-label {
                            font-size: 12px;
                            font-weight: bold;
                            color: #666;
                            text-transform: uppercase;
                            margin-bottom: 8px;
                            letter-spacing: 1px;
                        }
                        .produto-nome {
                            font-size: 16px;
                            font-weight: bold;
                            color: #96815d;
                            line-height: 1.4;
                            word-wrap: break-word;
                            word-break: break-word;
                            max-width: 100%;
                        }
                        .valor-section {
                            text-align: center;
                            padding-top: 15px;
                        }
                        .valor-label {
                            font-size: 12px;
                            font-weight: bold;
                            color: #666;
                            text-transform: uppercase;
                            margin-bottom: 8px;
                            letter-spacing: 1px;
                        }
                        .produto-valor {
                            font-size: 24px;
                            font-weight: bold;
                            color: #28a745;
                            display: block;
                        }
                        .produto-especificacoes {
                            background: white;
                            border: 2px solid #96815d;
                            border-radius: 10px;
                            padding: 25px;
                            margin: 25px 0;
                            page-break-inside: avoid;
                        }
                        .produto-especificacoes h3 {
                            color: #96815d;
                            margin-bottom: 20px;
                            font-size: 18px;
                            border-bottom: 2px solid #e0e0e0;
                            padding-bottom: 10px;
                        }
                        .especificacoes-grid {
                            display: grid;
                            gap: 15px;
                        }
                        .especificacao-item {
                            display: grid;
                            grid-template-columns: 140px 1fr;
                            gap: 10px;
                            align-items: start;
                        }
                        .espec-label {
                            font-weight: bold;
                            color: #666;
                            font-size: 14px;
                        }
                        .espec-value {
                            color: #333;
                            font-size: 14px;
                            word-wrap: break-word;
                        }
                        .valor-destaque {
                            background: #f8f9fa;
                            border: 3px solid #28a745;
                            border-radius: 15px;
                            padding: 25px;
                            text-align: center;
                            margin: 30px 0;
                            page-break-inside: avoid;
                        }
                        .valor-label {
                            font-size: 16px;
                            font-weight: bold;
                            color: #666;
                            margin-bottom: 10px;
                        }
                        .valor-principal {
                            font-size: 32px;
                            font-weight: bold;
                            color: #28a745;
                        }
                        .footer {
                            background: #96815d;
                            color: white;
                            margin-top: 40px;
                            padding: 20px;
                            text-align: center;
                            border-radius: 10px;
                        }
                        @media print {
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>SISOUL JOIAS</h1>
                        <div class="subtitle">Joias que tocam a alma</div>
                        <div style="margin-top: 20px; font-size: 16px; font-weight: bold;">
                            ORÇAMENTO Nº ${orcamento.id.substr(-6).toUpperCase()}
                        </div>
                    </div>

                    <div class="content">
                        <div class="section">
                            <h3>👤 Dados do Cliente</h3>
                            <div class="info-row">
                                <span class="label">Nome Completo:</span>
                                <span class="value">${orcamento.clienteNome}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">WhatsApp:</span>
                                <span class="value">${orcamento.clienteWhatsapp}</span>
                            </div>
                            ${orcamento.clienteEmail ? `
                            <div class="info-row">
                                <span class="label">Email:</span>
                                <span class="value">${orcamento.clienteEmail}</span>
                            </div>
                            ` : ''}
                            <div class="info-row">
                                <span class="label">Data do Orçamento:</span>
                                <span class="value">${orcamento.dataOrcamento} às ${orcamento.horaOrcamento}</span>
                            </div>
                        </div>

                        <div class="produto-especificacoes">
                            <h3>💎 Especificações do Produto</h3>
                            <div class="especificacoes-grid">
                                <div class="especificacao-item">
                                    <span class="espec-label">Nome da Joia:</span>
                                    <span class="espec-value">${orcamento.produtoNome}</span>
                                </div>
                                <div class="especificacao-item">
                                    <span class="espec-label">Material:</span>
                                    <span class="espec-value">${getMaterialLabel(orcamento.produtoMaterial)}</span>
                                </div>
                                ${orcamento.produtoPedras === 'sim' ? `
                                <div class="especificacao-item">
                                    <span class="espec-label">Pedras:</span>
                                    <span class="espec-value">${getTipoPedrasLabel(orcamento.produtoTipoPedras)}</span>
                                </div>
                                ` : ''}
                                ${orcamento.produtoObservacoes ? `
                                <div class="especificacao-item">
                                    <span class="espec-label">Observações:</span>
                                    <span class="espec-value">${orcamento.produtoObservacoes}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>

                        <div class="valor-destaque">
                            <div class="valor-label">💰 Valor Total</div>
                            <div class="valor-principal">${orcamento.valorFormatado}</div>
                        </div>

                        <div class="section">
                            <h3>📋 Condições Comerciais</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <div class="info-row">
                                        <span class="label">✓ Validade:</span>
                                        <span class="value">15 dias úteis</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">✓ Prazo de Entrega:</span>
                                        <span class="value">15 a 20 dias úteis</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">✓ Garantia:</span>
                                        <span class="value">6 meses</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="info-row">
                                        <span class="label">✓ Material:</span>
                                        <span class="value">Certificado</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">✓ Acabamento:</span>
                                        <span class="value">100% Artesanal</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">✓ Status:</span>
                                        <span class="value">${orcamento.status}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section">
                            <h3>📞 Informações de Contato</h3>
                            <div style="text-align: center;">
                                <p style="margin: 10px 0; font-size: 16px;">
                                    <strong>📱 WhatsApp:</strong> Entre em contato para dúvidas e acompanhamento
                                </p>
                                <p style="margin: 10px 0; font-size: 16px;">
                                    <strong>🏪 Atendimento:</strong> Segunda a Sexta, 9h às 18h
                                </p>
                                <p style="margin: 10px 0; font-size: 16px;">
                                    <strong>💎 Especialidades:</strong> Alianças, Anéis de Formatura e Joias Personalizadas
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="footer">
                        <h3 style="margin: 0 0 10px 0;">SISOUL JOIAS</h3>
                        <p style="margin: 5px 0; font-size: 14px;">Joias que tocam a alma</p>
                        <p style="margin: 8px 0; font-size: 13px;">WhatsApp: (85) 99868-4795 | Instagram: @sisouljoias</p>
                        <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">CNPJ: 40.640.553/0001-23 | Emitido em: ${new Date().toLocaleString('pt-BR')}</p>
                    </div>

                    <div class="no-print" style="text-align: center; margin-top: 30px;">
                        <button onclick="window.print()" style="background: #96815d; color: white; border: none; padding: 15px 30px; border-radius: 5px; font-size: 16px; cursor: pointer;">
                            📄 Imprimir/Salvar como PDF
                        </button>
                        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 15px 30px; border-radius: 5px; font-size: 16px; cursor: pointer; margin-left: 10px;">
                            ❌ Fechar
                        </button>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Aguardar carregamento e focar na janela
            setTimeout(() => {
                printWindow.focus();
            }, 500);

            console.log('✅ PDF alternativo gerado');

            if (window.SisoulUI) {
                SisoulUI.showToast('📄 Janela de impressão aberta! Use Ctrl+P para salvar como PDF', 'success', 5000);
            } else {
                alert('✅ Janela de impressão aberta!\n\nUse Ctrl+P ou clique em "Imprimir/Salvar como PDF" para gerar o arquivo.');
            }
        }
    }

    // Funções auxiliares para labels
    function getMaterialLabel(material) {
        const materiais = {
            'ouro18k': 'Ouro 18k',
            'prata925': 'Prata 925',
            'prata950': 'Prata 950',
            'aco': 'Aço Inoxidável',
            'folheado': 'Folheado a Ouro'
        };
        return materiais[material] || material;
    }

    function getTipoPedrasLabel(tipoPedras) {
        const tipos = {
            'sinteticas': 'Pedras Sintéticas (Zircônia)',
            'naturais': 'Pedras Naturais',
            'semipreciosas': 'Pedras Semipreciosas'
        };
        return tipos[tipoPedras] || tipoPedras;
    }

    // Verificar se cliente já existe pelo WhatsApp
    function verificarClienteExistente(whatsapp) {
        if (!window.SisoulStorage || !whatsapp) return null;

        const clientes = SisoulStorage.getCollection('clientes') || [];

        // Normalizar WhatsApp para comparação (remover caracteres especiais)
        const whatsappNormalizado = whatsapp.replace(/\D/g, '');

        return clientes.find(cliente => {
            const clienteWhatsapp = (cliente.whatsapp || '').replace(/\D/g, '');
            return clienteWhatsapp === whatsappNormalizado;
        });
    }

    // Converter orçamento para venda com verificação inteligente de cliente
    function converterParaVenda(id) {
        if (!window.SisoulStorage) {
            alert('❌ Sistema de storage não disponível!');
            return;
        }

        const orcamento = SisoulStorage.getItem('orcamentos', id);
        if (!orcamento) {
            alert('❌ Orçamento não encontrado!');
            return;
        }

        // Verificar se cliente já existe
        const clienteExistente = verificarClienteExistente(orcamento.clienteWhatsapp);

        if (!clienteExistente) {
            // Cliente não existe - precisa cadastrar primeiro
            const confirmacaoCadastro = confirm(
                `👤 CLIENTE PRECISA SER CADASTRADO\n\n` +
                `📋 Orçamento: #${orcamento.numeroOrcamento}\n` +
                `👤 Cliente: ${orcamento.clienteNome}\n` +
                `📱 WhatsApp: ${orcamento.clienteWhatsapp}\n\n` +
                `❗ Este cliente ainda não está cadastrado no sistema.\n` +
                `Para converter o orçamento em venda, é necessário\n` +
                `cadastrar o cliente primeiro.\n\n` +
                `✅ Deseja ir para o cadastro de clientes agora?`
            );

            if (!confirmacaoCadastro) {
                return;
            }

            // Salvar dados do orçamento para usar após cadastro
            sessionStorage.setItem('orcamento_para_conversao', JSON.stringify({
                orcamentoId: id,
                orcamento: orcamento,
                etapa: 'aguardando_cadastro_cliente'
            }));

            // Redirecionar para cadastro de clientes com dados pré-preenchidos
            const url = `clientes.html?acao=novo&nome=${encodeURIComponent(orcamento.clienteNome)}&whatsapp=${encodeURIComponent(orcamento.clienteWhatsapp)}&email=${encodeURIComponent(orcamento.clienteEmail || '')}&origem=orcamento`;

            window.location.href = url;
            return;
        }

        // Cliente existe - prosseguir com conversão
        const confirmacao = confirm(
            `💰 CONVERTER ORÇAMENTO PARA VENDA?\n\n` +
            `📋 Orçamento: #${orcamento.numeroOrcamento}\n` +
            `👤 Cliente: ${clienteExistente.nomeCompleto}\n` +
            `📱 WhatsApp: ${clienteExistente.whatsapp}\n` +
            `💎 Produto: ${orcamento.produtoDescricao}\n` +
            `💵 Valor: ${orcamento.valorFormatado}\n\n` +
            `✅ Cliente já cadastrado no sistema.\n` +
            `Deseja prosseguir com a conversão?`
        );

        if (!confirmacao) {
            return;
        }

        try {
            // Criar objeto da venda baseado no orçamento e cliente cadastrado
            const agora = new Date();
            const venda = {
                id: Date.now().toString(),
                clienteId: clienteExistente.id,
                clienteNome: clienteExistente.nomeCompleto,
                clienteWhatsapp: clienteExistente.whatsapp,
                clienteEmail: clienteExistente.email || '',
                tipoTransacao: 'orcamento_aprovado',
                // Dados detalhados do produto do orçamento
                produto: orcamento.produtoNome,
                material: orcamento.produtoMaterial,
                quantidade: 1,
                valorUnitario: orcamento.produtoValor,
                valorTotal: orcamento.produtoValor,
                valorTotalFormatado: orcamento.valorFormatado,
                // Especificações do produto
                produtoEspecificacoes: {
                    nome: orcamento.produtoNome,
                    material: orcamento.produtoMaterial,
                    pedras: orcamento.produtoPedras,
                    tipoPedras: orcamento.produtoTipoPedras,
                    observacoes: orcamento.produtoObservacoes
                },
                formaPagamento: 'A definir',
                statusPagamento: 'Pendente',
                dataEntrega: '',
                statusEntrega: 'pendente',
                observacoes: `Convertido do orçamento #${orcamento.numeroOrcamento}`,
                dataCompra: agora.toLocaleDateString('pt-BR'),
                horaCompra: agora.toLocaleTimeString('pt-BR'),
                origemOrcamento: orcamento.id,
                numeroOrcamento: orcamento.numeroOrcamento
            };

            // Salvar a venda
            SisoulStorage.saveItem('compras', venda);

            // Processar no estoque (integração automática)
            if (window.EstoqueModule) {
                try {
                    EstoqueModule.processarVenda(venda);
                    console.log('📦 Venda convertida processada no estoque');
                } catch (error) {
                    console.error('❌ Erro ao processar venda convertida no estoque:', error);
                }
            }

            // Marcar orçamento como convertido
            const orcamentoAtualizado = {
                ...orcamento,
                statusConversao: 'Convertido',
                dataConversao: agora.toLocaleDateString('pt-BR'),
                horaConversao: agora.toLocaleTimeString('pt-BR'),
                vendaId: venda.id,
                clienteId: clienteExistente.id
            };

            SisoulStorage.saveItem('orcamentos', orcamentoAtualizado);

            // Atualizar lista
            listarOrcamentos();

            const mensagem = `✅ ORÇAMENTO CONVERTIDO PARA VENDA!\n\n` +
                            `📋 Orçamento: #${orcamento.numeroOrcamento}\n` +
                            `👤 Cliente: ${clienteExistente.nomeCompleto}\n` +
                            `💰 Venda criada com sucesso!\n\n` +
                            `🎯 Próximos passos:\n` +
                            `• Vá para o módulo "💰 Vendas"\n` +
                            `• Complete os dados de pagamento\n` +
                            `• Finalize a venda`;

            alert(mensagem);

            // Redirecionar para vendas
            setTimeout(() => {
                window.location.href = 'compras.html';
            }, 2000);

            console.log('✅ Orçamento convertido para venda:', {
                orcamentoId: id,
                vendaId: venda.id,
                clienteId: clienteExistente.id,
                cliente: clienteExistente.nomeCompleto
            });

        } catch (error) {
            console.error('❌ Erro ao converter orçamento:', error);
            alert('❌ Erro ao converter orçamento: ' + error.message);
        }
    }

    // Excluir orçamento
    function excluirOrcamento(id) {
        if (confirm('🗑️ Deseja realmente excluir este orçamento?\n\nEsta ação não pode ser desfeita!')) {
            if (window.SisoulStorage) {
                SisoulStorage.removeItem('orcamentos', id);
            }
            listarOrcamentos();
            atualizarEstatisticas();
            alert('✅ Orçamento excluído com sucesso!');
        }
    }

    // Atualizar estatísticas
    function atualizarEstatisticas() {
        if (window.SisoulStorage && window.SisoulCore) {
            const orcamentos = SisoulStorage.getCollection('orcamentos');
            SisoulCore.updateStatistics({
                totalOrcamentos: orcamentos.length
            });
        }
    }

    // Função para formatar prazo de entrega
    function getPrazoEntregaLabel(prazo) {
        const prazos = {
            '7-10': '7 a 10 dias úteis',
            '10-12': '10 a 12 dias úteis',
            '12-15': '12 a 15 dias úteis'
        };
        return prazos[prazo] || 'Prazo não informado';
    }

    // Função para adicionar imagens da Sisoul nos PDFs
    function adicionarImagensSisoul(doc, pageWidth, pageHeight, corPrimaria) {
        // Tentar adicionar logo no canto superior direito
        const logoPath = './images/logo + nome.png';
        const fundoPath = './images/slogan + logo.png';

        // Criar elementos de imagem para pré-carregar
        const logoImg = new Image();
        const fundoImg = new Image();

        // Configurar logo
        logoImg.onload = function() {
            try {
                doc.addImage(logoImg, 'PNG', pageWidth - 70, 5, 60, 25);
                console.log('✅ Logo "logo + nome" adicionada com sucesso');
            } catch (error) {
                console.log('❌ Erro ao adicionar logo:', error);
                // Fallback para texto
                adicionarLogoTexto(doc, pageWidth, corPrimaria);
            }
        };

        logoImg.onerror = function() {
            console.log('❌ Erro ao carregar logo, usando texto');
            adicionarLogoTexto(doc, pageWidth, corPrimaria);
        };

        // Configurar fundo
        fundoImg.onload = function() {
            try {
                doc.addImage(fundoImg, 'PNG', pageWidth/2 - 40, pageHeight/2 - 25, 80, 50, undefined, 'NONE', 0.1);
                console.log('✅ Fundo "slogan + logo" adicionado com sucesso');
            } catch (error) {
                console.log('❌ Erro ao adicionar fundo:', error);
                // Fallback para marca d'água texto
                adicionarMarcaDaguaTexto(doc, pageWidth, pageHeight);
            }
        };

        fundoImg.onerror = function() {
            console.log('❌ Erro ao carregar fundo, usando marca d\'água texto');
            adicionarMarcaDaguaTexto(doc, pageWidth, pageHeight);
        };

        // Iniciar carregamento das imagens
        logoImg.src = logoPath;
        fundoImg.src = fundoPath;

        // Timeout para fallback se as imagens não carregarem
        setTimeout(() => {
            if (!logoImg.complete) {
                adicionarLogoTexto(doc, pageWidth, corPrimaria);
            }
            if (!fundoImg.complete) {
                adicionarMarcaDaguaTexto(doc, pageWidth, pageHeight);
            }
        }, 2000);
    }

    // Fallback: Logo como texto estilizado com cores da Sisoul
    function adicionarLogoTexto(doc, pageWidth, corPrimaria) {
        // Cor dourada da Sisoul para a logo
        const corDourada = [213, 169, 85]; // #d5a955

        // Símbolo do diamante
        doc.setTextColor(...corDourada);
        doc.setFontSize(16);
        doc.text('💎', pageWidth - 25, 12, { align: 'center' });

        // Nome SISOUL em estilo script
        doc.setTextColor(...corDourada);
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text('SISOUL JOIAS', pageWidth - 10, 18, { align: 'right' });

        // Slogan em fonte menor
        doc.setTextColor(...corPrimaria);
        doc.setFontSize(8);
        doc.setFont(undefined, 'italic');
        doc.text('Joias que tocam a alma', pageWidth - 10, 26, { align: 'right' });

        // S estilizado embaixo
        doc.setTextColor(...corDourada);
        doc.setFontSize(20);
        doc.setFont(undefined, 'bold');
        doc.text('S', pageWidth - 18, 35, { align: 'center' });
    }

    // Fallback: Marca d'água como texto estilizado
    function adicionarMarcaDaguaTexto(doc, pageWidth, pageHeight) {
        // Marca d'água muito sutil com cor dourada
        doc.setTextColor(213, 169, 85, 0.05); // #d5a955 com transparência
        doc.setFontSize(48);
        doc.setFont(undefined, 'bold');
        doc.text('SISOUL', pageWidth / 2, pageHeight / 2 - 10, {
            align: 'center',
            angle: 45
        });

        // Diamante sutil
        doc.setTextColor(213, 169, 85, 0.03);
        doc.setFontSize(32);
        doc.text('💎', pageWidth / 2, pageHeight / 2 + 15, {
            align: 'center',
            angle: 45
        });
    }

    // Gerar número sequencial do orçamento (formato: 01210625)
    function gerarNumeroOrcamento() {
        const hoje = new Date();
        const dia = String(hoje.getDate()).padStart(2, '0');
        const mes = String(hoje.getMonth() + 1).padStart(2, '0');
        const ano = String(hoje.getFullYear()).slice(-2);
        const dataFormatada = `${dia}${mes}${ano}`;

        // Obter todos os orçamentos para verificar números existentes do dia
        let maiorNumeroHoje = 0;
        if (window.SisoulStorage) {
            const todosOrcamentos = SisoulStorage.getCollection('orcamentos');

            // Filtrar orçamentos do dia atual que já têm numeroOrcamento
            todosOrcamentos.forEach(orcamento => {
                if (orcamento.numeroOrcamento && orcamento.numeroOrcamento.endsWith(dataFormatada)) {
                    // Extrair o número sequencial (primeiros 2 dígitos)
                    const numeroSequencial = parseInt(orcamento.numeroOrcamento.substring(0, 2));
                    if (!isNaN(numeroSequencial) && numeroSequencial > maiorNumeroHoje) {
                        maiorNumeroHoje = numeroSequencial;
                    }
                }
            });
        }

        // Próximo número sequencial
        const proximoNumero = maiorNumeroHoje + 1;
        const numeroSequencial = String(proximoNumero).padStart(2, '0');

        console.log(`🔢 Gerando número do orçamento: ${numeroSequencial}${dataFormatada}`);
        return `${numeroSequencial}${dataFormatada}`;
    }

    // Função para testar a numeração de orçamentos
    function testarNumeracaoOrcamento() {
        const numero = gerarNumeroOrcamento();
        const hoje = new Date();
        const dataHoje = hoje.toLocaleDateString('pt-BR');

        alert(`🔢 TESTE DE NUMERAÇÃO - ORÇAMENTOS\n\n` +
              `Data de hoje: ${dataHoje}\n` +
              `Próximo número: ${numero}\n\n` +
              `Formato: XX${hoje.getDate().toString().padStart(2,'0')}${(hoje.getMonth()+1).toString().padStart(2,'0')}${hoje.getFullYear().toString().slice(-2)}\n` +
              `Exemplo: 01210625 = 01 (primeiro) + 21/06/25`);
    }

    // Verificar se há conversão automática pendente via URL
    function verificarConversaoAutomatica() {
        const urlParams = new URLSearchParams(window.location.search);
        const converterOrcamentoId = urlParams.get('converter');

        if (converterOrcamentoId) {
            // Limpar URL
            window.history.replaceState({}, document.title, window.location.pathname);

            // Executar conversão automaticamente
            setTimeout(() => {
                console.log('🔄 Executando conversão automática do orçamento:', converterOrcamentoId);
                converterParaVenda(converterOrcamentoId);
            }, 1000);
        }
    }

    // Inicialização do módulo
    function init() {
        console.log('🧾 Inicializando módulo de Orçamentos...');

        // Escutar eventos do core
        if (window.SisoulCore) {
            SisoulCore.events.on('tabChanged', function(event) {
                if (event.detail.tabName === 'orcamentos') {
                    listarOrcamentos();
                }
            });
        }

        // Verificar conversão automática
        verificarConversaoAutomatica();

        isInitialized = true;
        console.log('✅ Módulo de Orçamentos inicializado');
    }

    // API pública do módulo
    return {
        init,
        testeBasico,
        novoOrcamento,
        fecharModal,
        salvarOrcamento,
        listarOrcamentos,
        gerarPDF,
        converterParaVenda,
        excluirOrcamento,
        togglePedrasDetalhes
    };
})();

console.log('🧾 Orcamentos.js carregado com sucesso!');

// Função para fechar modal
function fecharModalOrcamento() {
    console.log('🔒 Fechando modal de orçamento...');
    const modal = document.getElementById('modalOrcamentoFinal');
    if (modal) {
        modal.style.display = 'none';
        limparFormularioOrcamento();
    }
}

// Função para limpar formulário
function limparFormularioOrcamento() {
    const campos = [
        'nomeClienteFinal',
        'whatsappClienteFinal', 
        'produtoFinal',
        'valorFinal'
    ];
    
    campos.forEach(campo => {
        const elemento = document.getElementById(campo);
        if (elemento) {
            elemento.value = '';
        }
    });
}

// Função para salvar orçamento
function salvarOrcamentoFinal() {
    console.log('💾 Iniciando salvamento de orçamento...');
    
    // Capturar dados do formulário
    const nome = document.getElementById('nomeClienteFinal')?.value?.trim();
    const whatsapp = document.getElementById('whatsappClienteFinal')?.value?.trim();
    const produto = document.getElementById('produtoFinal')?.value?.trim();
    const valor = document.getElementById('valorFinal')?.value?.trim();
    
    // Validar campos obrigatórios
    if (!nome || !whatsapp || !produto || !valor) {
        alert('❌ Por favor, preencha todos os campos obrigatórios!');
        return;
    }
    
    // Validar valor numérico
    const valorNumerico = parseFloat(valor);
    if (isNaN(valorNumerico) || valorNumerico <= 0) {
        alert('❌ Por favor, insira um valor válido maior que zero!');
        return;
    }
    
    // Criar objeto do orçamento
    const orcamento = {
        id: proximoIdOrcamento++,
        clienteNome: nome,
        clienteWhatsapp: whatsapp,
        produto: produto,
        valor: valorNumerico,
        valorFormatado: valorNumerico.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }),
        dataOrcamento: new Date().toLocaleDateString('pt-BR'),
        horaOrcamento: new Date().toLocaleTimeString('pt-BR'),
        status: 'Pendente'
    };
    
    // Adicionar à lista
    orcamentosLista.push(orcamento);
    
    // Salvar no localStorage
    try {
        localStorage.setItem('orcamentosLista', JSON.stringify(orcamentosLista));
        console.log('💾 Orçamento salvo no localStorage');
    } catch (error) {
        console.error('❌ Erro ao salvar no localStorage:', error);
    }
    
    // Atualizar contador na interface
    const contadorOrcamentos = document.getElementById('orcamentos');
    if (contadorOrcamentos) {
        contadorOrcamentos.textContent = orcamentosLista.length;
    }
    
    // Mostrar confirmação
    const mensagem = `✅ ORÇAMENTO SALVO COM SUCESSO!\n\n` +
                    `👤 Cliente: ${nome}\n` +
                    `📱 WhatsApp: ${whatsapp}\n` +
                    `💎 Produto: ${produto}\n` +
                    `💰 Valor: ${orcamento.valorFormatado}\n` +
                    `📅 Data: ${orcamento.dataOrcamento}\n` +
                    `🕐 Hora: ${orcamento.horaOrcamento}`;
    
    alert(mensagem);
    
    // Fechar modal e limpar
    fecharModalOrcamento();
    
    // Atualizar lista se estiver na aba de orçamentos
    if (typeof carregarListaOrcamentos === 'function') {
        carregarListaOrcamentos();
    }
    
    console.log('✅ Orçamento salvo com sucesso:', orcamento);
}

// Função para carregar lista de orçamentos
function carregarListaOrcamentos() {
    console.log('📋 Carregando lista de orçamentos...');
    
    const container = document.getElementById('listaOrcamentos');
    if (!container) {
        console.log('📋 Container de lista não encontrado');
        return;
    }
    
    if (orcamentosLista.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <h4>🧾 Sistema de Orçamentos</h4>
                <p>Nenhum orçamento criado ainda.</p>
                <p>Clique em "➕ Novo Orçamento" para começar!</p>
                <br>
                <button class="btn success" onclick="abrirModalOrcamento()" style="font-size: 18px; padding: 15px 30px;">
                    ➕ Criar Primeiro Orçamento
                </button>
            </div>
        `;
        return;
    }
    
    let html = '';
    orcamentosLista.forEach(orc => {
        html += `
            <div class="orcamento-card" style="background: white; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <div style="font-size: 1.2rem; font-weight: bold; color: #96815d;">
                        Orçamento #${String(orc.id).padStart(3, '0')}
                    </div>
                    <div>
                        <button class="btn" onclick="gerarPDFOrcamento(${orc.id})" style="margin-right: 5px;">📄 PDF</button>
                        <button class="btn danger" onclick="excluirOrcamento(${orc.id})">🗑️ Excluir</button>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <span style="font-size: 0.9rem; color: #666;">Cliente</span><br>
                        <span style="font-weight: 600; color: #333;">👤 ${orc.clienteNome}</span>
                    </div>
                    <div>
                        <span style="font-size: 0.9rem; color: #666;">WhatsApp</span><br>
                        <span style="font-weight: 600; color: #333;">📱 ${orc.clienteWhatsapp}</span>
                    </div>
                    <div>
                        <span style="font-size: 0.9rem; color: #666;">Produto</span><br>
                        <span style="font-weight: 600; color: #333;">💎 ${orc.produto}</span>
                    </div>
                    <div>
                        <span style="font-size: 0.9rem; color: #666;">Valor</span><br>
                        <span style="font-weight: 600; color: #28a745; font-size: 1.1rem;">💰 ${orc.valorFormatado}</span>
                    </div>
                    <div>
                        <span style="font-size: 0.9rem; color: #666;">Data</span><br>
                        <span style="font-weight: 600; color: #333;">📅 ${orc.dataOrcamento}</span>
                    </div>
                    <div>
                        <span style="font-size: 0.9rem; color: #666;">Status</span><br>
                        <span style="font-weight: 600; color: #333;">📊 ${orc.status}</span>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    console.log(`✅ Lista carregada com ${orcamentosLista.length} orçamentos`);
}

// Função para gerar PDF (simulada)
function gerarPDFOrcamento(id) {
    const orcamento = orcamentosLista.find(o => o.id === id);
    if (!orcamento) {
        alert('❌ Orçamento não encontrado!');
        return;
    }
    
    const mensagem = `📄 PDF do Orçamento #${String(id).padStart(3, '0')}\n\n` +
                    `Esta funcionalidade geraria um PDF com:\n\n` +
                    `👤 Cliente: ${orcamento.clienteNome}\n` +
                    `📱 WhatsApp: ${orcamento.clienteWhatsapp}\n` +
                    `💎 Produto: ${orcamento.produto}\n` +
                    `💰 Valor: ${orcamento.valorFormatado}\n` +
                    `📅 Data: ${orcamento.dataOrcamento}`;
    
    alert(mensagem);
}

// Função para excluir orçamento
function excluirOrcamento(id) {
    if (!confirm('🗑️ Deseja realmente excluir este orçamento?\n\nEsta ação não pode ser desfeita!')) {
        return;
    }
    
    orcamentosLista = orcamentosLista.filter(o => o.id !== id);
    
    // Salvar no localStorage
    try {
        localStorage.setItem('orcamentosLista', JSON.stringify(orcamentosLista));
    } catch (error) {
        console.error('❌ Erro ao salvar no localStorage:', error);
    }
    
    // Atualizar contador
    const contadorOrcamentos = document.getElementById('orcamentos');
    if (contadorOrcamentos) {
        contadorOrcamentos.textContent = orcamentosLista.length;
    }
    
    // Recarregar lista
    carregarListaOrcamentos();
    
    alert('✅ Orçamento excluído com sucesso!');
}

// Carregar orçamentos do localStorage na inicialização
function carregarOrcamentosDoStorage() {
    try {
        const dadosSalvos = localStorage.getItem('orcamentosLista');
        if (dadosSalvos) {
            orcamentosLista = JSON.parse(dadosSalvos);
            proximoIdOrcamento = Math.max(...orcamentosLista.map(o => o.id), 0) + 1;
            console.log(`📂 ${orcamentosLista.length} orçamentos carregados do storage`);
        }
    } catch (error) {
        console.error('❌ Erro ao carregar orçamentos do storage:', error);
        orcamentosLista = [];
        proximoIdOrcamento = 1;
    }
}

// Inicializar orçamentos quando o arquivo carregar
carregarOrcamentosDoStorage();

console.log('🧾 Orcamentos.js carregado com sucesso!');
