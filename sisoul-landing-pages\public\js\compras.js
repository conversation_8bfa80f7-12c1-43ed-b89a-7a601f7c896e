// SISOUL JOIAS - MÓDULO DE COMPRAS
// Gestão completa de compras/vendas

window.ComprasModule = (function() {
    'use strict';

    const MODAL_ID = 'modalCompras';
    let isInitialized = false;

    // Criar modal dinamicamente
    function createModal() {
        const modalHTML = `
            <div id="${MODAL_ID}" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>💰 Nova Compra/Venda</h3>
                        <span class="close" onclick="ComprasModule.fecharModal()">&times;</span>
                    </div>
                    <div class="form-section">
                        <h4>👤 Cliente</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Cliente *</label>
                                <select class="form-input" id="compraCliente" required>
                                    <option value="">Selecione um cliente</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Tipo de Transação *</label>
                                <select class="form-input" id="tipoTransacao" required>
                                    <option value="">Selecione</option>
                                    <option value="venda">💰 Venda</option>
                                    <option value="orcamento">🧾 Orçamento Aprovado</option>
                                    <option value="encomenda">📦 Encomenda</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <h4>💎 Produto</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Produto *</label>
                                <input type="text" class="form-input" id="compraProduto" placeholder="Ex: Aliança de prata 925" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Material *</label>
                                <select class="form-input" id="compraMaterial" required>
                                    <option value="">Selecione</option>
                                    <option value="prata925">Prata 925</option>
                                    <option value="prata950">Prata 950</option>
                                    <option value="ouro18k">Ouro 18k</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Quantidade *</label>
                                <input type="number" class="form-input" id="compraQuantidade" min="1" value="1" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Valor Unitário (R$) *</label>
                                <input type="number" class="form-input" id="compraValorUnitario" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Valor Total (R$)</label>
                                <input type="number" class="form-input" id="compraValorTotal" step="0.01" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <h4>💳 Pagamento</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Forma de Pagamento *</label>
                                <select class="form-input" id="formaPagamento" required>
                                    <option value="">Selecione</option>
                                    <option value="dinheiro">💵 Dinheiro</option>
                                    <option value="pix">📱 PIX</option>
                                    <option value="cartao_debito">💳 Cartão Débito</option>
                                    <option value="cartao_credito">💳 Cartão Crédito</option>
                                    <option value="transferencia">🏦 Transferência</option>
                                    <option value="parcelado">📅 Parcelado</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Status do Pagamento *</label>
                                <select class="form-input" id="statusPagamento" required>
                                    <option value="">Selecione</option>
                                    <option value="pago">✅ Pago</option>
                                    <option value="pendente">⏳ Pendente</option>
                                    <option value="parcial">📊 Parcial</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <h4>📅 Entrega</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Data de Entrega</label>
                                <input type="date" class="form-input" id="dataEntrega">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Status da Entrega</label>
                                <select class="form-input" id="statusEntrega">
                                    <option value="pendente">⏳ Pendente</option>
                                    <option value="producao">🔨 Em Produção</option>
                                    <option value="pronto">✅ Pronto</option>
                                    <option value="entregue">📦 Entregue</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Tipo de Produção *</label>
                                <select class="form-input" id="tipoProducao" required onchange="ComprasModule.toggleCustoFields()">
                                    <option value="">Selecione o tipo</option>
                                    <option value="producao_propria">💎 Produção Própria</option>
                                    <option value="fornecedor">🏪 Compra de Fornecedor</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Seção de Custos - Produção Própria -->
                    <div id="custosProducao" class="form-section" style="display: none;">
                        <h4>💎 Custos de Produção Própria</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Prata Usada (gramas)</label>
                                <input type="number" class="form-input" id="prataUsada" step="0.1" min="0" placeholder="0.0">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Ouro Usado (gramas)</label>
                                <input type="number" class="form-input" id="ouroUsado" step="0.1" min="0" placeholder="0.0">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Outros Materiais (R$)</label>
                                <input type="number" class="form-input" id="outrosMateriais" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>

                        <h5 style="color: #96815d; margin: 20px 0 15px 0; border-bottom: 2px solid #96815d; padding-bottom: 5px;">
                            🔨 Mão de Obra Detalhada
                        </h5>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">🔥 Fundição (R$)</label>
                                <input type="number" class="form-input" id="maoObraFundicao" step="0.01" min="0" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label class="form-label">✨ Acabamento (R$)</label>
                                <input type="number" class="form-input" id="maoObraAcabamento" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">💎 Cravação (R$)</label>
                                <input type="number" class="form-input" id="maoObraCravacao" step="0.01" min="0" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🔨 Total Mão de Obra</label>
                                <input type="number" class="form-input" id="maoObraTotal" step="0.01" readonly style="background: #f8f4ef; font-weight: bold; color: #96815d;">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Custo Total Estimado</label>
                                <input type="number" class="form-input" id="custoTotalProducao" step="0.01" readonly style="background: #f8f4ef; font-weight: bold;">
                            </div>
                        </div>
                    </div>

                    <!-- Seção de Custos - Fornecedor -->
                    <div id="custosFornecedor" class="form-section" style="display: none;">
                        <h4>🏪 Compra de Fornecedor</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Nome do Fornecedor</label>
                                <input type="text" class="form-input" id="nomeFornecedor" placeholder="Nome do fornecedor">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Custo de Compra (R$)</label>
                                <input type="number" class="form-input" id="custoCompra" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Frete/Taxas (R$)</label>
                                <input type="number" class="form-input" id="freteTaxas" step="0.01" min="0" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Custo Total</label>
                                <input type="number" class="form-input" id="custoTotalFornecedor" step="0.01" readonly style="background: #f8f4ef; font-weight: bold;">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Observações</label>
                                <textarea class="form-input" id="compraObservacoes" rows="3" placeholder="Observações sobre a compra..."></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="form-section">
                        <div style="text-align: right;">
                            <button type="button" class="btn" onclick="ComprasModule.fecharModal()">Cancelar</button>
                            <button type="button" class="btn success" onclick="ComprasModule.salvarCompra()">💰 Salvar Compra</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const container = document.getElementById('modalsContainer') || document.body;
        container.insertAdjacentHTML('beforeend', modalHTML);

        // Configurar eventos
        setupModalEvents();

        console.log('💰 Modal de compras criado');
    }

    // Configurar eventos do modal
    function setupModalEvents() {
        // Calcular total automaticamente
        const quantidade = document.getElementById('compraQuantidade');
        const valorUnitario = document.getElementById('compraValorUnitario');

        if (quantidade && valorUnitario) {
            [quantidade, valorUnitario].forEach(input => {
                input.addEventListener('input', calcularTotal);
            });
        }

        // Configurar eventos para cálculo de custos
        setupCustoEvents();
    }

    // Configurar eventos para campos de custo
    function setupCustoEvents() {
        // Eventos para produção própria
        const camposProducao = ['prataUsada', 'ouroUsado', 'outrosMateriais', 'maoObraFundicao', 'maoObraAcabamento', 'maoObraCravacao'];
        camposProducao.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) {
                elemento.addEventListener('input', calcularCustoProducao);
            }
        });

        // Eventos para fornecedor
        const camposFornecedor = ['custoCompra', 'freteTaxas'];
        camposFornecedor.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) {
                elemento.addEventListener('input', calcularCustoFornecedor);
            }
        });
    }

    // Alternar campos de custo baseado no tipo de produção
    window.toggleCustoFields = function() {
        const tipoProducao = document.getElementById('tipoProducao')?.value;
        const custosProducao = document.getElementById('custosProducao');
        const custosFornecedor = document.getElementById('custosFornecedor');

        if (custosProducao && custosFornecedor) {
            if (tipoProducao === 'producao_propria') {
                custosProducao.style.display = 'block';
                custosFornecedor.style.display = 'none';
                limparCamposFornecedor();
            } else if (tipoProducao === 'fornecedor') {
                custosProducao.style.display = 'none';
                custosFornecedor.style.display = 'block';
                limparCamposProducao();
            } else {
                custosProducao.style.display = 'none';
                custosFornecedor.style.display = 'none';
                limparCamposProducao();
                limparCamposFornecedor();
            }
        }
    };

    // Calcular custo de produção própria
    function calcularCustoProducao() {
        const prataUsada = parseFloat(document.getElementById('prataUsada')?.value) || 0;
        const ouroUsado = parseFloat(document.getElementById('ouroUsado')?.value) || 0;
        const outrosMateriais = parseFloat(document.getElementById('outrosMateriais')?.value) || 0;

        // Mão de obra detalhada
        const maoObraFundicao = parseFloat(document.getElementById('maoObraFundicao')?.value) || 0;
        const maoObraAcabamento = parseFloat(document.getElementById('maoObraAcabamento')?.value) || 0;
        const maoObraCravacao = parseFloat(document.getElementById('maoObraCravacao')?.value) || 0;
        const maoObraTotal = maoObraFundicao + maoObraAcabamento + maoObraCravacao;

        // Atualizar campo total de mão de obra
        const maoObraTotalInput = document.getElementById('maoObraTotal');
        if (maoObraTotalInput) {
            maoObraTotalInput.value = maoObraTotal.toFixed(2);
        }

        // Obter preços dos metais do localStorage
        const estoqueMetais = JSON.parse(localStorage.getItem('sisoulEstoqueMetais') || '{"prata":{"quantidade":0,"valorPorGrama":0},"ouro":{"quantidade":0,"valorPorGrama":0}}');

        const custoPrata = prataUsada * estoqueMetais.prata.valorPorGrama;
        const custoOuro = ouroUsado * estoqueMetais.ouro.valorPorGrama;
        const custoTotal = custoPrata + custoOuro + outrosMateriais + maoObraTotal;

        const custoTotalInput = document.getElementById('custoTotalProducao');
        if (custoTotalInput) {
            custoTotalInput.value = custoTotal.toFixed(2);
        }

        // Verificar disponibilidade de estoque
        verificarDisponibilidadeEstoque(prataUsada, ouroUsado);
    }

    // Verificar disponibilidade de estoque em tempo real
    function verificarDisponibilidadeEstoque(prataNeeded, ouroNeeded) {
        const estoqueMetais = JSON.parse(localStorage.getItem('sisoulEstoqueMetais') || '{"prata":{"quantidade":0,"valorPorGrama":0},"ouro":{"quantidade":0,"valorPorGrama":0}}');

        // Limpar alertas anteriores
        const alertContainer = document.getElementById('estoqueAlerts');
        if (alertContainer) {
            alertContainer.remove();
        }

        const alertas = [];
        const sugestoes = [];

        // Verificar prata
        if (prataNeeded > 0) {
            if (estoqueMetais.prata.quantidade < prataNeeded) {
                const faltaPrata = prataNeeded - estoqueMetais.prata.quantidade;
                alertas.push(`🥈 PRATA: Faltam ${faltaPrata.toFixed(1)}g (disponível: ${estoqueMetais.prata.quantidade}g)`);
                const custoCompra = faltaPrata * estoqueMetais.prata.valorPorGrama;
                sugestoes.push(`💰 Comprar ${faltaPrata.toFixed(1)}g de prata: R$ ${custoCompra.toFixed(2)} (R$ ${estoqueMetais.prata.valorPorGrama.toFixed(2)}/g)`);
            } else {
                alertas.push(`✅ PRATA: Disponível ${estoqueMetais.prata.quantidade}g (necessário: ${prataNeeded}g)`);
            }
        }

        // Verificar ouro
        if (ouroNeeded > 0) {
            if (estoqueMetais.ouro.quantidade < ouroNeeded) {
                const faltaOuro = ouroNeeded - estoqueMetais.ouro.quantidade;
                alertas.push(`🥇 OURO: Faltam ${faltaOuro.toFixed(1)}g (disponível: ${estoqueMetais.ouro.quantidade}g)`);
                const custoCompra = faltaOuro * estoqueMetais.ouro.valorPorGrama;
                sugestoes.push(`💰 Comprar ${faltaOuro.toFixed(1)}g de ouro: R$ ${custoCompra.toFixed(2)} (R$ ${estoqueMetais.ouro.valorPorGrama.toFixed(2)}/g)`);
            } else {
                alertas.push(`✅ OURO: Disponível ${estoqueMetais.ouro.quantidade}g (necessário: ${ouroNeeded}g)`);
            }
        }

        // Mostrar alertas na interface
        if (alertas.length > 0) {
            mostrarAlertasEstoque(alertas, sugestoes);
        }
    }

    // Mostrar alertas de estoque na interface
    function mostrarAlertasEstoque(alertas, sugestoes) {
        const custosProducao = document.getElementById('custosProducao');
        if (!custosProducao) return;

        const alertContainer = document.createElement('div');
        alertContainer.id = 'estoqueAlerts';
        alertContainer.style.cssText = `
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            background: #fff3cd;
        `;

        let html = '<strong>📦 Verificação de Estoque:</strong><br>';
        alertas.forEach(alerta => {
            const cor = alerta.includes('✅') ? '#28a745' : '#dc3545';
            html += `<div style="color: ${cor}; margin: 5px 0;">${alerta}</div>`;
        });

        if (sugestoes.length > 0) {
            html += '<br><strong>💡 Sugestões de Compra:</strong><br>';
            sugestoes.forEach(sugestao => {
                html += `<div style="color: #17a2b8; margin: 5px 0;">${sugestao}</div>`;
            });
        }

        alertContainer.innerHTML = html;
        custosProducao.appendChild(alertContainer);
    }

    // Calcular custo de fornecedor
    function calcularCustoFornecedor() {
        const custoCompra = parseFloat(document.getElementById('custoCompra')?.value) || 0;
        const freteTaxas = parseFloat(document.getElementById('freteTaxas')?.value) || 0;
        const custoTotal = custoCompra + freteTaxas;

        const custoTotalInput = document.getElementById('custoTotalFornecedor');
        if (custoTotalInput) {
            custoTotalInput.value = custoTotal.toFixed(2);
        }
    }

    // Limpar campos de produção
    function limparCamposProducao() {
        const campos = ['prataUsada', 'ouroUsado', 'outrosMateriais', 'maoObraFundicao', 'maoObraAcabamento', 'maoObraCravacao', 'maoObraTotal', 'custoTotalProducao'];
        campos.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) elemento.value = '';
        });
    }

    // Limpar campos de fornecedor
    function limparCamposFornecedor() {
        const campos = ['nomeFornecedor', 'custoCompra', 'freteTaxas', 'custoTotalFornecedor'];
        campos.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) elemento.value = '';
        });
    }

    // Calcular valor total
    function calcularTotal() {
        const quantidade = parseFloat(document.getElementById('compraQuantidade')?.value) || 0;
        const valorUnitario = parseFloat(document.getElementById('compraValorUnitario')?.value) || 0;
        const total = quantidade * valorUnitario;

        const valorTotalInput = document.getElementById('compraValorTotal');
        if (valorTotalInput) {
            valorTotalInput.value = total.toFixed(2);
        }
    }

    // Carregar clientes no select
    function carregarClientes() {
        const select = document.getElementById('compraCliente');
        if (!select) return;

        // Limpar opções existentes (exceto a primeira)
        select.innerHTML = '<option value="">Selecione um cliente</option>';

        if (window.SisoulStorage) {
            const clientes = SisoulStorage.getCollection('clientes');
            clientes.forEach(cliente => {
                const option = document.createElement('option');
                option.value = cliente.id;
                option.textContent = `${cliente.nomeCompleto} - ${cliente.whatsapp}`;
                select.appendChild(option);
            });
        }
    }

    // Abrir modal de nova compra
    function novaCompra() {
        console.log('💰 Abrindo modal de nova compra...');

        let modal = document.getElementById(MODAL_ID);
        if (!modal) {
            createModal();
            modal = document.getElementById(MODAL_ID);
        }

        // Carregar clientes
        carregarClientes();

        if (modal) {
            modal.style.display = 'block';
            console.log('✅ Modal de compra aberto');
        } else {
            console.error('❌ Erro ao criar/abrir modal');
            alert('❌ Erro ao abrir modal de compra');
        }
    }

    // Fechar modal
    function fecharModal() {
        const modal = document.getElementById(MODAL_ID);
        if (modal) {
            modal.style.display = 'none';
            limparFormulario();
            console.log('🔒 Modal de compra fechado');
        }
    }

    // Limpar formulário
    function limparFormulario() {
        const campos = [
            'compraCliente', 'tipoTransacao', 'compraProduto', 'compraMaterial',
            'compraQuantidade', 'compraValorUnitario', 'compraValorTotal',
            'formaPagamento', 'statusPagamento', 'dataEntrega', 'statusEntrega',
            'compraObservacoes', 'tipoProducao'
        ];
        campos.forEach(campo => {
            const elemento = document.getElementById(campo);
            if (elemento) {
                if (elemento.type === 'number' && campo === 'compraQuantidade') {
                    elemento.value = '1';
                } else {
                    elemento.value = '';
                }
            }
        });

        // Limpar campos de custo
        limparCamposProducao();
        limparCamposFornecedor();

        // Esconder seções de custo
        const custosProducao = document.getElementById('custosProducao');
        const custosFornecedor = document.getElementById('custosFornecedor');
        if (custosProducao) custosProducao.style.display = 'none';
        if (custosFornecedor) custosFornecedor.style.display = 'none';
    }

    // Salvar compra
    function salvarCompra() {
        console.log('💰 Salvando compra...');

        // Capturar dados básicos
        const dados = {
            clienteId: document.getElementById('compraCliente')?.value,
            tipoTransacao: document.getElementById('tipoTransacao')?.value,
            produto: document.getElementById('compraProduto')?.value?.trim(),
            material: document.getElementById('compraMaterial')?.value,
            quantidade: parseInt(document.getElementById('compraQuantidade')?.value) || 1,
            valorUnitario: parseFloat(document.getElementById('compraValorUnitario')?.value) || 0,
            valorTotal: parseFloat(document.getElementById('compraValorTotal')?.value) || 0,
            formaPagamento: document.getElementById('formaPagamento')?.value,
            statusPagamento: document.getElementById('statusPagamento')?.value,
            dataEntrega: document.getElementById('dataEntrega')?.value,
            statusEntrega: document.getElementById('statusEntrega')?.value || 'pendente',
            observacoes: document.getElementById('compraObservacoes')?.value?.trim(),
            tipoProducao: document.getElementById('tipoProducao')?.value
        };

        // Capturar dados de custo baseado no tipo de produção
        if (dados.tipoProducao === 'producao_propria') {
            dados.custosProducao = {
                prataUsada: parseFloat(document.getElementById('prataUsada')?.value) || 0,
                ouroUsado: parseFloat(document.getElementById('ouroUsado')?.value) || 0,
                outrosMateriais: parseFloat(document.getElementById('outrosMateriais')?.value) || 0,
                maoDeObra: {
                    fundicao: parseFloat(document.getElementById('maoObraFundicao')?.value) || 0,
                    acabamento: parseFloat(document.getElementById('maoObraAcabamento')?.value) || 0,
                    cravacao: parseFloat(document.getElementById('maoObraCravacao')?.value) || 0,
                    total: parseFloat(document.getElementById('maoObraTotal')?.value) || 0
                },
                custoTotal: parseFloat(document.getElementById('custoTotalProducao')?.value) || 0
            };
        } else if (dados.tipoProducao === 'fornecedor') {
            dados.custosFornecedor = {
                nomeFornecedor: document.getElementById('nomeFornecedor')?.value?.trim() || '',
                custoCompra: parseFloat(document.getElementById('custoCompra')?.value) || 0,
                freteTaxas: parseFloat(document.getElementById('freteTaxas')?.value) || 0,
                custoTotal: parseFloat(document.getElementById('custoTotalFornecedor')?.value) || 0
            };
        }

        // Validar campos obrigatórios
        if (!dados.clienteId || !dados.tipoTransacao || !dados.produto ||
            !dados.material || !dados.valorUnitario || !dados.formaPagamento ||
            !dados.statusPagamento || !dados.tipoProducao) {
            alert('❌ Por favor, preencha todos os campos obrigatórios, incluindo o tipo de produção!');
            return;
        }

        // Validar estoque para produção própria
        if (dados.tipoProducao === 'producao_propria') {
            const prataUsada = dados.custosProducao?.prataUsada || 0;
            const ouroUsado = dados.custosProducao?.ouroUsado || 0;

            const estoqueMetais = JSON.parse(localStorage.getItem('sisoulEstoqueMetais') || '{"prata":{"quantidade":0,"valorPorGrama":0},"ouro":{"quantidade":0,"valorPorGrama":0}}');

            const alertas = [];
            const sugestoes = [];

            if (prataUsada > estoqueMetais.prata.quantidade) {
                const faltaPrata = prataUsada - estoqueMetais.prata.quantidade;
                alertas.push(`🥈 PRATA: Faltam ${faltaPrata.toFixed(1)}g`);
                sugestoes.push(`Comprar ${faltaPrata.toFixed(1)}g de prata: ~R$ ${(faltaPrata * estoqueMetais.prata.valorPorGrama).toFixed(2)}`);
            }

            if (ouroUsado > estoqueMetais.ouro.quantidade) {
                const faltaOuro = ouroUsado - estoqueMetais.ouro.quantidade;
                alertas.push(`🥇 OURO: Faltam ${faltaOuro.toFixed(1)}g`);
                sugestoes.push(`Comprar ${faltaOuro.toFixed(1)}g de ouro: ~R$ ${(faltaOuro * estoqueMetais.ouro.valorPorGrama).toFixed(2)}`);
            }

            if (alertas.length > 0) {
                let mensagem = '⚠️ ESTOQUE INSUFICIENTE!\n\n';
                mensagem += alertas.join('\n') + '\n\n';
                mensagem += '💡 SUGESTÕES:\n';
                mensagem += sugestoes.join('\n') + '\n\n';
                mensagem += 'Deseja continuar mesmo assim?\n';
                mensagem += '(O estoque ficará negativo)';

                if (!confirm(mensagem)) {
                    return;
                }
            }
        }

        // Obter dados do cliente
        let clienteNome = 'Cliente não encontrado';
        if (window.SisoulStorage) {
            const cliente = SisoulStorage.getItem('clientes', dados.clienteId);
            if (cliente) {
                clienteNome = cliente.nomeCompleto;
            }
        }

        // Gerar número sequencial do pedido
        const numeroPedido = gerarNumeroPedido();

        // Criar objeto da compra
        const compra = {
            ...dados,
            clienteNome,
            numeroPedido,
            valorTotalFormatado: new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(dados.valorTotal),
            dataCompra: new Date().toLocaleDateString('pt-BR'),
            horaCompra: new Date().toLocaleTimeString('pt-BR')
        };

        // Salvar usando o módulo de storage
        if (window.SisoulStorage) {
            const compraSalva = SisoulStorage.saveItem('compras', compra);
            console.log('✅ Compra salva:', compraSalva);
        }

        // Processar no estoque (integração automática)
        if (window.EstoqueModule) {
            try {
                EstoqueModule.processarVenda(compra);
                console.log('📦 Venda processada no estoque automaticamente');
            } catch (error) {
                console.error('❌ Erro ao processar venda no estoque:', error);
            }
        }

        // Deduzir do estoque se for produção própria
        let mensagemEstoque = '';
        if (dados.tipoProducao === 'producao_propria' && dados.custosProducao) {
            const prataUsada = dados.custosProducao.prataUsada || 0;
            const ouroUsado = dados.custosProducao.ouroUsado || 0;

            if (prataUsada > 0 || ouroUsado > 0) {
                const estoqueMetais = JSON.parse(localStorage.getItem('sisoulEstoqueMetais') || '{"prata":{"quantidade":0,"valorPorGrama":0},"ouro":{"quantidade":0,"valorPorGrama":0}}');

                // Deduzir do estoque
                estoqueMetais.prata.quantidade = Math.max(0, estoqueMetais.prata.quantidade - prataUsada);
                estoqueMetais.ouro.quantidade = Math.max(0, estoqueMetais.ouro.quantidade - ouroUsado);

                // Salvar estoque atualizado
                localStorage.setItem('sisoulEstoqueMetais', JSON.stringify(estoqueMetais));

                mensagemEstoque = `\n\n📦 ESTOQUE ATUALIZADO:\n`;
                if (prataUsada > 0) {
                    mensagemEstoque += `🥈 Prata: -${prataUsada}g (restam: ${estoqueMetais.prata.quantidade}g)\n`;
                }
                if (ouroUsado > 0) {
                    mensagemEstoque += `🥇 Ouro: -${ouroUsado}g (restam: ${estoqueMetais.ouro.quantidade}g)`;
                }
            }
        }

        // Mostrar confirmação
        const mensagem = `✅ COMPRA SALVA COM SUCESSO!\n\n` +
                        `👤 Cliente: ${clienteNome}\n` +
                        `💎 Produto: ${dados.produto}\n` +
                        `💰 Valor: ${compra.valorTotalFormatado}\n` +
                        `📅 Data: ${compra.dataCompra}` +
                        mensagemEstoque;

        alert(mensagem);

        // Fechar modal
        fecharModal();

        // Atualizar lista
        listarCompras();

        // Notificar outros módulos sobre a atualização
        if (window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('sisoulVendaAtualizada', {
                detail: { compra: compra, tipo: 'nova' }
            }));
        }

        // Atualizar estatísticas
        atualizarEstatisticas();
    }

    // Variáveis de paginação para compras
    let currentPageCompras = 1;
    const itemsPerPageCompras = 10;

    // Listar compras com paginação
    function listarCompras(page = 1) {
        console.log('💰 Carregando lista de compras...');

        const container = document.getElementById('comprasList');
        if (!container) {
            console.log('❌ Container de lista não encontrado');
            return;
        }

        currentPageCompras = page;

        // Obter compras do storage
        let compras = [];
        if (window.SisoulStorage) {
            compras = SisoulStorage.getCollection('compras');
            // Ordenar por data mais recente primeiro
            compras.sort((a, b) => {
                const dateA = new Date(a.dataCompra.split('/').reverse().join('-') + ' ' + a.horaCompra);
                const dateB = new Date(b.dataCompra.split('/').reverse().join('-') + ' ' + b.horaCompra);
                return dateB - dateA;
            });
        }

        if (compras.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h4>💰 Sistema de Compras/Vendas</h4>
                    <p>Nenhuma compra registrada ainda.</p>
                    <p>Clique em "➕ Nova Compra" para começar!</p>
                    <br>
                    <button class="btn success" onclick="ComprasModule.novaCompra()" style="font-size: 18px; padding: 15px 30px;">
                        ➕ Registrar Primeira Compra
                    </button>
                </div>
            `;
            return;
        }

        // Calcular paginação
        const totalItems = compras.length;
        const totalPages = Math.ceil(totalItems / itemsPerPageCompras);
        const startIndex = (currentPageCompras - 1) * itemsPerPageCompras;
        const endIndex = startIndex + itemsPerPageCompras;
        const comprasPagina = compras.slice(startIndex, endIndex);

        // Gerar HTML da lista
        let html = '';

        // Informações de paginação
        html += `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 15px; background: #f8f4ef; border-radius: 8px;">
                <div style="color: #96815d; font-weight: bold;">
                    📊 Mostrando ${startIndex + 1}-${Math.min(endIndex, totalItems)} de ${totalItems} compras/vendas
                </div>
                <div style="color: #666;">
                    📅 Ordenados por data (mais recentes primeiro)
                </div>
            </div>
        `;

        comprasPagina.forEach(compra => {
            const statusPagamentoClass = getStatusClass(compra.statusPagamento);
            const statusEntregaClass = getStatusClass(compra.statusEntrega);

            html += `
                <div class="item-card">
                    <div class="item-header">
                        <div class="item-title">💰 Pedido ${compra.numeroPedido || compra.id.substr(-6)} - ${compra.clienteNome}</div>
                        <div>
                            <button class="btn" onclick="ComprasModule.gerarGuiaPedido('${compra.id}')">📄 Guia do Pedido</button>
                            <button class="btn" onclick="ComprasModule.editarCompra('${compra.id}')">✏️ Editar</button>
                            <button class="btn danger" onclick="ComprasModule.excluirCompra('${compra.id}')">🗑️ Excluir</button>
                        </div>
                    </div>
                    <div class="item-info">
                        <div class="info-item">
                            <span class="info-label">Produto</span>
                            <span class="info-value">💎 ${compra.produto}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Material</span>
                            <span class="info-value">✨ ${getMaterialLabel(compra.material)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Quantidade</span>
                            <span class="info-value">📦 ${compra.quantidade}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Valor Total</span>
                            <span class="info-value" style="color: #28a745; font-weight: bold;">💰 ${compra.valorTotalFormatado}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Pagamento</span>
                            <span class="info-value">💳 ${getFormaPagamentoLabel(compra.formaPagamento)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Status Pagamento</span>
                            <span class="info-value" style="color: ${statusPagamentoClass}">📊 ${getStatusPagamentoLabel(compra.statusPagamento)}</span>
                        </div>
                        ${compra.statusPagamento === 'parcial' ? `
                            <div class="info-item">
                                <span class="info-label">Valor Pago (60%)</span>
                                <span class="info-value" style="color: #28a745; font-weight: bold;">💰 ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.valorTotal * 0.6)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Saldo Restante (40%)</span>
                                <span class="info-value" style="color: #dc3545; font-weight: bold;">💰 ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.valorTotal * 0.4)}</span>
                            </div>
                        ` : ''}
                        <div class="info-item">
                            <span class="info-label">Status Entrega</span>
                            <span class="info-value" style="color: ${statusEntregaClass}">📦 ${getStatusEntregaLabel(compra.statusEntrega)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Data Compra</span>
                            <span class="info-value">📅 ${compra.dataCompra}</span>
                        </div>
                        ${compra.tipoProducao ? `
                            <div class="info-item">
                                <span class="info-label">Tipo Produção</span>
                                <span class="info-value">${compra.tipoProducao === 'producao_propria' ? '💎 Produção Própria' : '🏪 Fornecedor'}</span>
                            </div>
                        ` : ''}
                        ${compra.custosProducao ? `
                            <div class="info-item">
                                <span class="info-label">Custo Produção</span>
                                <span class="info-value" style="color: #dc3545; font-weight: bold;">💸 ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.custoTotal)}</span>
                            </div>
                        ` : ''}
                        ${compra.custosFornecedor ? `
                            <div class="info-item">
                                <span class="info-label">Custo Fornecedor</span>
                                <span class="info-value" style="color: #dc3545; font-weight: bold;">💸 ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosFornecedor.custoTotal)}</span>
                            </div>
                        ` : ''}
                        ${(compra.custosProducao || compra.custosFornecedor) ? `
                            <div class="info-item">
                                <span class="info-label">Lucro Estimado</span>
                                <span class="info-value" style="color: #28a745; font-weight: bold;">💰 ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.valorTotal - (compra.custosProducao?.custoTotal || compra.custosFornecedor?.custoTotal || 0))}</span>
                            </div>
                        ` : ''}
                    </div>
                    ${compra.observacoes ? `
                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                            <strong>📝 Observações:</strong> ${compra.observacoes}
                        </div>
                    ` : ''}
                    ${compra.custosProducao ? `
                        <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                            <strong>💎 Detalhes da Produção:</strong><br>
                            <strong>Materiais:</strong> Prata: ${compra.custosProducao.prataUsada}g | Ouro: ${compra.custosProducao.ouroUsado}g<br>
                            <strong>Outros Materiais:</strong> ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.outrosMateriais)}<br>
                            <strong>Mão de Obra:</strong><br>
                            ${compra.custosProducao.maoDeObra && typeof compra.custosProducao.maoDeObra === 'object' ? `
                                • 🔥 Fundição: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.maoDeObra.fundicao)}<br>
                                • ✨ Acabamento: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.maoDeObra.acabamento)}<br>
                                • 💎 Cravação: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.maoDeObra.cravacao)}<br>
                                • <strong>Total M.O.: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.maoDeObra.total)}</strong>
                            ` : `
                                • Total: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosProducao.maoDeObra || 0)}
                            `}
                        </div>
                    ` : ''}
                    ${compra.custosFornecedor ? `
                        <div style="margin-top: 15px; padding: 15px; background: #d1ecf1; border-radius: 5px; border-left: 4px solid #17a2b8;">
                            <strong>🏪 Detalhes do Fornecedor:</strong><br>
                            Fornecedor: ${compra.custosFornecedor.nomeFornecedor}<br>
                            Custo: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosFornecedor.custoCompra)} |
                            Frete/Taxas: ${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.custosFornecedor.freteTaxas)}
                        </div>
                    ` : ''}
                </div>
            `;
        });

        // Adicionar controles de paginação
        if (totalPages > 1) {
            html += `
                <div style="display: flex; justify-content: center; align-items: center; margin-top: 30px; gap: 10px;">
                    <button class="btn" onclick="ComprasModule.listarCompras(${currentPageCompras - 1})"
                            ${currentPageCompras === 1 ? 'disabled style="opacity: 0.5;"' : ''}>
                        ← Anterior
                    </button>

                    <div style="display: flex; gap: 5px;">
            `;

            for (let i = 1; i <= totalPages; i++) {
                const isActive = i === currentPageCompras;
                html += `
                    <button class="btn ${isActive ? 'success' : ''}"
                            onclick="ComprasModule.listarCompras(${i})"
                            style="${isActive ? 'background: #96815d; color: white;' : 'background: white; color: #96815d;'} min-width: 40px;">
                        ${i}
                    </button>
                `;
            }

            html += `
                    </div>

                    <button class="btn" onclick="ComprasModule.listarCompras(${currentPageCompras + 1})"
                            ${currentPageCompras === totalPages ? 'disabled style="opacity: 0.5;"' : ''}>
                        Próxima →
                    </button>
                </div>
            `;
        }

        container.innerHTML = html;
        console.log(`✅ Lista carregada com ${comprasPagina.length} compras (página ${currentPageCompras} de ${totalPages})`);
    }

    // Funções utilitárias
    function getTipoTransacaoLabel(tipo) {
        const tipos = {
            'venda': 'Venda',
            'orcamento': 'Orçamento Aprovado',
            'encomenda': 'Encomenda'
        };
        return tipos[tipo] || tipo;
    }

    function getMaterialLabel(material) {
        const materiais = {
            'prata925': 'Prata 925',
            'prata950': 'Prata 950',
            'ouro18k': 'Ouro 18k'
        };
        return materiais[material] || material;
    }

    function getFormaPagamentoLabel(forma) {
        const formas = {
            'dinheiro': 'Dinheiro',
            'pix': 'PIX',
            'cartao_debito': 'Cartão Débito',
            'cartao_credito': 'Cartão Crédito',
            'transferencia': 'Transferência',
            'parcelado': 'Parcelado'
        };
        return formas[forma] || forma;
    }

    function getStatusPagamentoLabel(status) {
        const statuses = {
            'pago': 'Pago',
            'pendente': 'Pendente',
            'parcial': 'Parcial'
        };
        return statuses[status] || status;
    }

    function getStatusEntregaLabel(status) {
        const statuses = {
            'pendente': 'Pendente',
            'producao': 'Em Produção',
            'pronto': 'Pronto',
            'entregue': 'Entregue'
        };
        return statuses[status] || status;
    }

    function getStatusClass(status) {
        const classes = {
            'pago': '#28a745',
            'entregue': '#28a745',
            'pronto': '#28a745',
            'pendente': '#ffc107',
            'parcial': '#fd7e14',
            'producao': '#17a2b8'
        };
        return classes[status] || '#6c757d';
    }

    // Gerar Guia do Pedido em PDF
    function gerarGuiaPedido(id) {
        if (window.SisoulStorage) {
            const compra = SisoulStorage.getItem('compras', id);
            if (!compra) {
                alert('❌ Compra não encontrada!');
                return;
            }

            // Verificar se a biblioteca PDF está disponível
            if (!window.jsPDF && !window.jspdf && !window.sisoulPDFAvailable) {
                gerarGuiaAlternativa(compra);
                return;
            }

            try {
                gerarGuiaPDFProfissional(compra);
            } catch (error) {
                console.error('❌ Erro ao gerar PDF:', error);
                gerarGuiaAlternativa(compra);
            }
        }
    }

    // Gerar PDF profissional da guia do pedido
    function gerarGuiaPDFProfissional(compra) {
        // Tentar diferentes formas de acessar jsPDF
        let jsPDFClass;
        if (window.jsPDF && window.jsPDF.jsPDF) {
            jsPDFClass = window.jsPDF.jsPDF;
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
        } else if (window.jspdf) {
            jsPDFClass = window.jspdf.jsPDF;
        }

        if (!jsPDFClass) {
            throw new Error('jsPDF class not found');
        }

        const doc = new jsPDFClass();

        // Configurações e cores da marca
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 20;
        let yPosition = 25;

        // Cores da Sisoul Joias
        const corPrimaria = [150, 129, 93]; // #96815d
        const corSecundaria = [248, 244, 239]; // #f8f4ef
        const corTexto = [51, 51, 51]; // #333333
        const corDestaque = [40, 167, 69]; // #28a745

        // Cabeçalho com fundo colorido
        doc.setFillColor(...corPrimaria);
        doc.rect(0, 0, pageWidth, 50, 'F');

        // Carregar e adicionar as imagens da Sisoul
        adicionarImagensSisoulPedido(doc, pageWidth, pageHeight, corPrimaria);

        // Título principal (sem símbolos)
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(24);
        doc.setFont(undefined, 'bold');
        doc.text('SISOUL JOIAS', pageWidth / 2, 25, { align: 'center' });

        doc.setFontSize(14);
        doc.setFont(undefined, 'normal');
        doc.text('Joias que tocam a alma', pageWidth / 2, 40, { align: 'center' });

        doc.setFontSize(10);
        doc.text('Guia do Pedido - Via do Cliente', pageWidth / 2, 50, { align: 'center' });

        // Reset cor do texto
        doc.setTextColor(...corTexto);
        yPosition = 65;

        // Título do documento com fundo
        doc.setFillColor(...corSecundaria);
        doc.rect(margin, yPosition - 5, pageWidth - (margin * 2), 20, 'F');

        doc.setFontSize(18);
        doc.setFont(undefined, 'bold');
        doc.setTextColor(...corPrimaria);
        doc.text(`PEDIDO Nº ${compra.numeroPedido || compra.id.substr(-6).toUpperCase()}`, pageWidth / 2, yPosition + 8, { align: 'center' });

        yPosition += 30;

        // SEÇÃO: DADOS DO CLIENTE
        createSectionGuia(doc, 'DADOS DO CLIENTE', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
        yPosition += 20;

        const dadosCliente = [
            { label: 'Nome:', valor: compra.clienteNome },
            { label: 'Data do Pedido:', valor: `${compra.dataCompra} às ${compra.horaCompra}` },
            { label: 'Tipo:', valor: getTipoTransacaoLabel(compra.tipoTransacao) }
        ];

        dadosCliente.forEach(item => {
            doc.setFont(undefined, 'bold');
            doc.setFontSize(10);
            doc.text(item.label, margin + 5, yPosition);

            doc.setFont(undefined, 'normal');
            doc.text(item.valor, margin + 45, yPosition);
            yPosition += 8;
        });

        yPosition += 10;

        // SEÇÃO: DETALHES DO PRODUTO
        createSectionGuia(doc, 'DETALHES DO PRODUTO', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
        yPosition += 20;

        // Produto em destaque
        doc.setFillColor(255, 255, 255);
        doc.setDrawColor(...corPrimaria);
        doc.rect(margin + 5, yPosition - 5, pageWidth - (margin * 2) - 10, 35, 'FD');

        doc.setFont(undefined, 'bold');
        doc.setFontSize(14);
        doc.setTextColor(...corPrimaria);
        doc.text(compra.produto, margin + 10, yPosition + 5);

        doc.setFont(undefined, 'normal');
        doc.setFontSize(12);
        doc.setTextColor(...corTexto);
        doc.text(`Material: ${getMaterialLabel(compra.material)}`, margin + 10, yPosition + 15);
        doc.text(`Quantidade: ${compra.quantidade}`, margin + 10, yPosition + 25);

        doc.setFont(undefined, 'bold');
        doc.setFontSize(16);
        doc.setTextColor(...corDestaque);
        doc.text(compra.valorTotalFormatado, pageWidth - margin - 10, yPosition + 15, { align: 'right' });

        yPosition += 45;

        // SEÇÃO: INFORMAÇÕES DE PAGAMENTO
        createSectionGuia(doc, 'INFORMAÇÕES DE PAGAMENTO', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
        yPosition += 20;

        // Calcular valores para pagamento parcial
        const valorTotal = compra.valorTotal;
        const valorParcial60 = valorTotal * 0.6;
        const valorRestante40 = valorTotal * 0.4;
        const valorParcial60Formatado = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(valorParcial60);
        const valorRestante40Formatado = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(valorRestante40);

        const dadosPagamento = [
            { label: 'Forma de Pagamento:', valor: getFormaPagamentoLabel(compra.formaPagamento) },
            { label: 'Status do Pagamento:', valor: getStatusPagamentoLabel(compra.statusPagamento) },
            { label: 'Valor Total:', valor: compra.valorTotalFormatado }
        ];

        dadosPagamento.forEach(item => {
            doc.setFont(undefined, 'bold');
            doc.setFontSize(10);
            doc.text(item.label, margin + 5, yPosition);

            doc.setFont(undefined, 'normal');
            doc.text(item.valor, margin + 55, yPosition);
            yPosition += 8;
        });

        // Adicionar informações de pagamento parcial se aplicável
        if (compra.statusPagamento === 'parcial') {
            yPosition += 5;

            // Fundo destacado para pagamento parcial
            doc.setFillColor(255, 243, 205); // Amarelo claro
            doc.rect(margin + 5, yPosition - 3, pageWidth - (margin * 2) - 10, 25, 'F');

            doc.setFont(undefined, 'bold');
            doc.setFontSize(11);
            doc.setTextColor(...corPrimaria);
            doc.text('💰 DETALHES DO PAGAMENTO PARCIAL:', margin + 10, yPosition + 5);

            doc.setFont(undefined, 'normal');
            doc.setFontSize(10);
            doc.setTextColor(...corTexto);
            doc.text(`Valor pago (60%): ${valorParcial60Formatado}`, margin + 10, yPosition + 15);
            doc.text(`Saldo restante (40%): ${valorRestante40Formatado}`, margin + 10, yPosition + 25);

            yPosition += 30;
        }

        yPosition += 15;

        // SEÇÃO: INFORMAÇÕES DE ENTREGA
        createSectionGuia(doc, 'INFORMAÇÕES DE ENTREGA', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
        yPosition += 20;

        const dadosEntrega = [
            { label: 'Status da Entrega:', valor: getStatusEntregaLabel(compra.statusEntrega) },
            { label: 'Previsão de Entrega:', valor: compra.dataEntrega ? new Date(compra.dataEntrega).toLocaleDateString('pt-BR') : 'A definir' },
            { label: 'Prazo Estimado:', valor: '15 a 20 dias úteis' }
        ];

        dadosEntrega.forEach(item => {
            doc.setFont(undefined, 'bold');
            doc.setFontSize(10);
            doc.text(item.label, margin + 5, yPosition);

            doc.setFont(undefined, 'normal');
            doc.text(item.valor, margin + 55, yPosition);
            yPosition += 8;
        });

        // Observações se existirem
        if (compra.observacoes) {
            yPosition += 15;
            createSectionGuia(doc, 'OBSERVAÇÕES', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
            yPosition += 20;

            doc.setFont(undefined, 'normal');
            doc.setFontSize(10);
            const observacoes = doc.splitTextToSize(compra.observacoes, pageWidth - (margin * 2) - 10);
            doc.text(observacoes, margin + 5, yPosition);
            yPosition += observacoes.length * 5;
        }

        // SEÇÃO: TERMOS E CONDIÇÕES
        yPosition += 20;
        createSectionGuia(doc, 'TERMOS E CONDIÇÕES', yPosition, pageWidth, margin, corPrimaria, corSecundaria);
        yPosition += 20;

        const termos = [
            '• Produto 100% artesanal, pequenas variações são características naturais',
            '• Garantia de 6 meses contra defeitos de fabricação',
            '• Acompanha certificado de autenticidade do material',
            '• Em caso de dúvidas, entre em contato conosco',
            '• Guarde este documento como comprovante do seu pedido'
        ];

        doc.setFont(undefined, 'normal');
        doc.setFontSize(9);
        doc.setTextColor(...corTexto);

        termos.forEach(termo => {
            doc.text(termo, margin + 5, yPosition);
            yPosition += 6;
        });

        // RODAPÉ ESTILIZADO
        const rodapeY = pageHeight - 40;

        // Fundo do rodapé
        doc.setFillColor(...corPrimaria);
        doc.rect(0, rodapeY, pageWidth, 40, 'F');

        // Texto do rodapé
        doc.setTextColor(255, 255, 255);
        doc.setFont(undefined, 'bold');
        doc.setFontSize(12);
        doc.text('SISOUL JOIAS', pageWidth / 2, rodapeY + 8, { align: 'center' });

        doc.setFont(undefined, 'normal');
        doc.setFontSize(9);
        doc.text('Joias que tocam a alma', pageWidth / 2, rodapeY + 16, { align: 'center' });

        // Informações de contato
        doc.setFontSize(8);
        doc.text('WhatsApp: (85) 99868-4795 | Instagram: @sisouljoias', pageWidth / 2, rodapeY + 24, { align: 'center' });

        // CNPJ e informações do pedido
        doc.setFontSize(7);
        doc.text(`CNPJ: 40.640.553/0001-23 | Pedido: ${compra.numeroPedido || compra.id.substr(-6).toUpperCase()} | Emitido em: ${new Date().toLocaleString('pt-BR')}`, pageWidth / 2, rodapeY + 32, { align: 'center' });

        // Salvar PDF
        const fileName = `Guia_Pedido_Sisoul_${compra.id.substr(-6).toUpperCase()}_${compra.clienteNome.replace(/\s+/g, '_')}.pdf`;
        doc.save(fileName);

        if (window.SisoulUI) {
            SisoulUI.showToast('📄 Guia do pedido gerada com sucesso!', 'success');
        } else {
            alert('✅ Guia do pedido gerada e baixada com sucesso!');
        }
    }

    // Editar compra
    function editarCompra(id) {
        if (window.SisoulStorage) {
            const compra = SisoulStorage.getItem('compras', id);
            if (!compra) {
                alert('❌ Compra não encontrada!');
                return;
            }

            // Abrir modal de edição
            let modal = document.getElementById(MODAL_ID);
            if (!modal) {
                createModal();
                modal = document.getElementById(MODAL_ID);
            }

            // Carregar clientes
            carregarClientes();

            // Preencher formulário com dados da compra
            preencherFormularioEdicao(compra);

            // Alterar título do modal
            const modalHeader = modal.querySelector('.modal-header h3');
            if (modalHeader) {
                modalHeader.textContent = '✏️ Editar Compra/Venda';
            }

            // Alterar botão de salvar
            const saveButton = modal.querySelector('.btn.success');
            if (saveButton) {
                saveButton.textContent = '✏️ Atualizar Compra';
                saveButton.onclick = () => atualizarCompra(id);
            }

            modal.style.display = 'block';
            console.log('✅ Modal de edição aberto para compra:', id);
        }
    }

    // Preencher formulário para edição
    function preencherFormularioEdicao(compra) {
        const campos = {
            'compraCliente': compra.clienteId,
            'tipoTransacao': compra.tipoTransacao,
            'compraProduto': compra.produto,
            'compraMaterial': compra.material,
            'compraQuantidade': compra.quantidade,
            'compraValorUnitario': compra.valorUnitario,
            'compraValorTotal': compra.valorTotal,
            'formaPagamento': compra.formaPagamento,
            'statusPagamento': compra.statusPagamento,
            'dataEntrega': compra.dataEntrega,
            'statusEntrega': compra.statusEntrega,
            'compraObservacoes': compra.observacoes
        };

        Object.keys(campos).forEach(campoId => {
            const elemento = document.getElementById(campoId);
            if (elemento && campos[campoId] !== undefined) {
                elemento.value = campos[campoId];
            }
        });
    }

    // Atualizar compra existente
    function atualizarCompra(id) {
        console.log('✏️ Atualizando compra...');

        // Capturar dados (mesmo código do salvarCompra)
        const dados = {
            clienteId: document.getElementById('compraCliente')?.value,
            tipoTransacao: document.getElementById('tipoTransacao')?.value,
            produto: document.getElementById('compraProduto')?.value?.trim(),
            material: document.getElementById('compraMaterial')?.value,
            quantidade: parseInt(document.getElementById('compraQuantidade')?.value) || 1,
            valorUnitario: parseFloat(document.getElementById('compraValorUnitario')?.value) || 0,
            valorTotal: parseFloat(document.getElementById('compraValorTotal')?.value) || 0,
            formaPagamento: document.getElementById('formaPagamento')?.value,
            statusPagamento: document.getElementById('statusPagamento')?.value,
            dataEntrega: document.getElementById('dataEntrega')?.value,
            statusEntrega: document.getElementById('statusEntrega')?.value || 'pendente',
            observacoes: document.getElementById('compraObservacoes')?.value?.trim()
        };

        // Validar campos obrigatórios
        if (!dados.clienteId || !dados.tipoTransacao || !dados.produto ||
            !dados.material || !dados.valorUnitario || !dados.formaPagamento ||
            !dados.statusPagamento) {
            alert('❌ Por favor, preencha todos os campos obrigatórios!');
            return;
        }

        // Obter dados do cliente
        let clienteNome = 'Cliente não encontrado';
        if (window.SisoulStorage) {
            const cliente = SisoulStorage.getItem('clientes', dados.clienteId);
            if (cliente) {
                clienteNome = cliente.nomeCompleto;
            }
        }

        // Obter compra existente para manter dados originais
        const compraExistente = SisoulStorage.getItem('compras', id);

        // Criar objeto da compra atualizada
        const compraAtualizada = {
            ...dados,
            id: id, // Manter ID original
            clienteNome,
            valorTotalFormatado: new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(dados.valorTotal),
            dataCompra: compraExistente.dataCompra, // Manter data original
            horaCompra: compraExistente.horaCompra, // Manter hora original
            dataAtualizacao: new Date().toLocaleDateString('pt-BR'),
            horaAtualizacao: new Date().toLocaleTimeString('pt-BR')
        };

        // Salvar usando o módulo de storage
        if (window.SisoulStorage) {
            const compraSalva = SisoulStorage.saveItem('compras', compraAtualizada);
            console.log('✅ Compra atualizada:', compraSalva);
        }

        // Mostrar confirmação
        const mensagem = `✅ COMPRA ATUALIZADA COM SUCESSO!\n\n` +
                        `👤 Cliente: ${clienteNome}\n` +
                        `💎 Produto: ${dados.produto}\n` +
                        `💰 Valor: ${compraAtualizada.valorTotalFormatado}\n` +
                        `📅 Atualizada em: ${compraAtualizada.dataAtualizacao}`;

        alert(mensagem);

        // Resetar modal para modo de criação
        resetarModalParaCriacao();

        // Fechar modal
        fecharModal();

        // Atualizar lista
        listarCompras();

        // Notificar outros módulos sobre a atualização
        if (window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('sisoulVendaAtualizada', {
                detail: { compra: compraAtualizada, tipo: 'editada' }
            }));
        }

        // Atualizar estatísticas
        atualizarEstatisticas();
    }

    // Resetar modal para modo de criação
    function resetarModalParaCriacao() {
        const modal = document.getElementById(MODAL_ID);
        if (modal) {
            // Restaurar título original
            const modalHeader = modal.querySelector('.modal-header h3');
            if (modalHeader) {
                modalHeader.textContent = '💰 Nova Compra/Venda';
            }

            // Restaurar botão de salvar
            const saveButton = modal.querySelector('.btn.success');
            if (saveButton) {
                saveButton.textContent = '💰 Salvar Compra';
                saveButton.onclick = () => ComprasModule.salvarCompra();
            }
        }
    }

    // Gerar número sequencial do pedido (formato: 01210625)
    function gerarNumeroPedido() {
        const hoje = new Date();
        const dia = String(hoje.getDate()).padStart(2, '0');
        const mes = String(hoje.getMonth() + 1).padStart(2, '0');
        const ano = String(hoje.getFullYear()).slice(-2);
        const dataFormatada = `${dia}${mes}${ano}`;

        // Obter todas as compras para verificar números existentes do dia
        let maiorNumeroHoje = 0;
        if (window.SisoulStorage) {
            const todasCompras = SisoulStorage.getCollection('compras');

            // Filtrar compras do dia atual que já têm numeroPedido
            todasCompras.forEach(compra => {
                if (compra.numeroPedido && compra.numeroPedido.endsWith(dataFormatada)) {
                    // Extrair o número sequencial (primeiros 2 dígitos)
                    const numeroSequencial = parseInt(compra.numeroPedido.substring(0, 2));
                    if (!isNaN(numeroSequencial) && numeroSequencial > maiorNumeroHoje) {
                        maiorNumeroHoje = numeroSequencial;
                    }
                }
            });
        }

        // Próximo número sequencial
        const proximoNumero = maiorNumeroHoje + 1;
        const numeroSequencial = String(proximoNumero).padStart(2, '0');

        console.log(`🔢 Gerando número do pedido: ${numeroSequencial}${dataFormatada}`);
        return `${numeroSequencial}${dataFormatada}`;
    }

    // Função para adicionar imagens da Sisoul nos PDFs de pedido
    function adicionarImagensSisoulPedido(doc, pageWidth, pageHeight, corPrimaria) {
        // Tentar adicionar logo no canto superior direito
        const logoPath = './images/logo + nome.png';
        const fundoPath = './images/slogan + logo.png';

        // Criar elementos de imagem para pré-carregar
        const logoImg = new Image();
        const fundoImg = new Image();

        // Configurar logo
        logoImg.onload = function() {
            try {
                doc.addImage(logoImg, 'PNG', pageWidth - 70, 5, 60, 25);
                console.log('✅ Logo "logo + nome" adicionada com sucesso no pedido');
            } catch (error) {
                console.log('❌ Erro ao adicionar logo no pedido:', error);
                // Fallback para texto
                adicionarLogoTextoPedido(doc, pageWidth, corPrimaria);
            }
        };

        logoImg.onerror = function() {
            console.log('❌ Erro ao carregar logo no pedido, usando texto');
            adicionarLogoTextoPedido(doc, pageWidth, corPrimaria);
        };

        // Configurar fundo
        fundoImg.onload = function() {
            try {
                doc.addImage(fundoImg, 'PNG', pageWidth/2 - 40, pageHeight/2 - 25, 80, 50, undefined, 'NONE', 0.1);
                console.log('✅ Fundo "slogan + logo" adicionado com sucesso no pedido');
            } catch (error) {
                console.log('❌ Erro ao adicionar fundo no pedido:', error);
                // Fallback para marca d'água texto
                adicionarMarcaDaguaTextoPedido(doc, pageWidth, pageHeight);
            }
        };

        fundoImg.onerror = function() {
            console.log('❌ Erro ao carregar fundo no pedido, usando marca d\'água texto');
            adicionarMarcaDaguaTextoPedido(doc, pageWidth, pageHeight);
        };

        // Iniciar carregamento das imagens
        logoImg.src = logoPath;
        fundoImg.src = fundoPath;

        // Timeout para fallback se as imagens não carregarem
        setTimeout(() => {
            if (!logoImg.complete) {
                adicionarLogoTextoPedido(doc, pageWidth, corPrimaria);
            }
            if (!fundoImg.complete) {
                adicionarMarcaDaguaTextoPedido(doc, pageWidth, pageHeight);
            }
        }, 2000);
    }

    // Fallback: Logo como texto estilizado com cores da Sisoul no pedido
    function adicionarLogoTextoPedido(doc, pageWidth, corPrimaria) {
        // Cor dourada da Sisoul para a logo
        const corDourada = [213, 169, 85]; // #d5a955

        // Símbolo do diamante
        doc.setTextColor(...corDourada);
        doc.setFontSize(16);
        doc.text('💎', pageWidth - 25, 12, { align: 'center' });

        // Nome SISOUL em estilo script
        doc.setTextColor(...corDourada);
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text('SISOUL JOIAS', pageWidth - 10, 18, { align: 'right' });

        // Slogan em fonte menor
        doc.setTextColor(...corPrimaria);
        doc.setFontSize(8);
        doc.setFont(undefined, 'italic');
        doc.text('Joias que tocam a alma', pageWidth - 10, 26, { align: 'right' });

        // S estilizado embaixo
        doc.setTextColor(...corDourada);
        doc.setFontSize(20);
        doc.setFont(undefined, 'bold');
        doc.text('S', pageWidth - 18, 35, { align: 'center' });
    }

    // Fallback: Marca d'água como texto estilizado no pedido
    function adicionarMarcaDaguaTextoPedido(doc, pageWidth, pageHeight) {
        // Marca d'água muito sutil com cor dourada
        doc.setTextColor(213, 169, 85, 0.05); // #d5a955 com transparência
        doc.setFontSize(48);
        doc.setFont(undefined, 'bold');
        doc.text('SISOUL', pageWidth / 2, pageHeight / 2 - 10, {
            align: 'center',
            angle: 45
        });

        // Diamante sutil
        doc.setTextColor(213, 169, 85, 0.03);
        doc.setFontSize(32);
        doc.text('💎', pageWidth / 2, pageHeight / 2 + 15, {
            align: 'center',
            angle: 45
        });
    }

    // Função para testar a numeração (para debug)
    function testarNumeracao() {
        const numero = gerarNumeroPedido();
        const hoje = new Date();
        const dataHoje = hoje.toLocaleDateString('pt-BR');

        alert(`🔢 TESTE DE NUMERAÇÃO\n\n` +
              `Data de hoje: ${dataHoje}\n` +
              `Próximo número: ${numero}\n\n` +
              `Formato: XX${hoje.getDate().toString().padStart(2,'0')}${(hoje.getMonth()+1).toString().padStart(2,'0')}${hoje.getFullYear().toString().slice(-2)}\n` +
              `Exemplo: 01210625 = 01 (primeiro) + 21/06/25`);
    }

    // Excluir compra
    function excluirCompra(id) {
        if (confirm('🗑️ Deseja realmente excluir esta compra?\n\nEsta ação não pode ser desfeita!')) {
            if (window.SisoulStorage) {
                SisoulStorage.removeItem('compras', id);
            }
            listarCompras();
            atualizarEstatisticas();

            // Notificar outros módulos sobre a exclusão
            if (window.dispatchEvent) {
                window.dispatchEvent(new CustomEvent('sisoulVendaAtualizada', {
                    detail: { compraId: id, tipo: 'excluida' }
                }));
            }

            alert('✅ Compra excluída com sucesso!');
        }
    }

    // Atualizar estatísticas
    function atualizarEstatisticas() {
        if (window.SisoulStorage && window.SisoulCore) {
            const compras = SisoulStorage.getCollection('compras');
            SisoulCore.updateStatistics({
                totalCompras: compras.length
            });
        }
    }

    // Inicialização do módulo
    function init() {
        console.log('💰 Inicializando módulo de Compras...');

        // Escutar eventos do core
        if (window.SisoulCore) {
            SisoulCore.events.on('tabChanged', function(event) {
                if (event.detail.tabName === 'compras') {
                    listarCompras();
                }
            });
        }

        isInitialized = true;
        console.log('✅ Módulo de Compras inicializado');
    }

    // Função auxiliar para criar seções estilizadas na guia
    function createSectionGuia(doc, titulo, yPosition, pageWidth, margin, corPrimaria, corSecundaria) {
        // Fundo da seção
        doc.setFillColor(...corSecundaria);
        doc.rect(margin, yPosition - 3, pageWidth - (margin * 2), 15, 'F');

        // Título da seção
        doc.setFont(undefined, 'bold');
        doc.setFontSize(12);
        doc.setTextColor(...corPrimaria);
        doc.text(titulo, margin + 5, yPosition + 7);

        // Linha decorativa
        doc.setDrawColor(...corPrimaria);
        doc.setLineWidth(0.5);
        doc.line(margin + 5, yPosition + 10, pageWidth - margin - 5, yPosition + 10);
    }

    // Método alternativo para gerar guia (usando window.print)
    function gerarGuiaAlternativa(compra) {
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Guia do Pedido - Sisoul Joias</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        line-height: 1.6;
                        color: #333;
                    }
                    .header {
                        background: linear-gradient(135deg, #96815d 0%, #b8a082 100%);
                        color: white;
                        text-align: center;
                        padding: 30px 20px;
                        margin-bottom: 30px;
                    }
                    .header h1 {
                        color: white;
                        margin: 0;
                        font-size: 28px;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    }
                    .content {
                        padding: 0 30px;
                    }
                    .section {
                        margin-bottom: 30px;
                        background: #f8f4ef;
                        border-radius: 10px;
                        padding: 20px;
                        border-left: 5px solid #96815d;
                    }
                    .section h3 {
                        color: #96815d;
                        margin: 0 0 15px 0;
                        font-size: 18px;
                        border-bottom: 2px solid #96815d;
                        padding-bottom: 8px;
                    }
                    .produto-destaque {
                        background: white;
                        border: 2px solid #96815d;
                        border-radius: 10px;
                        padding: 20px;
                        text-align: center;
                        margin: 20px 0;
                    }
                    .produto-nome {
                        font-size: 20px;
                        font-weight: bold;
                        color: #96815d;
                        margin-bottom: 10px;
                    }
                    .produto-valor {
                        font-size: 24px;
                        font-weight: bold;
                        color: #28a745;
                    }
                    .info-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                    }
                    .info-item {
                        margin: 8px 0;
                    }
                    .label {
                        font-weight: bold;
                        color: #96815d;
                        display: inline-block;
                        min-width: 140px;
                    }
                    .footer {
                        background: #96815d;
                        color: white;
                        margin-top: 40px;
                        padding: 20px;
                        text-align: center;
                        border-radius: 10px;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>SISOUL JOIAS</h1>
                    <div style="font-size: 14px; margin-top: 5px;">Joias que tocam a alma</div>
                    <div style="font-size: 12px; margin-top: 10px; font-style: italic;">Guia do Pedido - Via do Cliente</div>
                    <div style="margin-top: 20px; font-size: 16px; font-weight: bold;">
                        PEDIDO Nº ${compra.numeroPedido || compra.id.substr(-6).toUpperCase()}
                    </div>
                </div>

                <div class="content">
                    <div class="section">
                        <h3>👤 Dados do Cliente</h3>
                        <div class="info-item">
                            <span class="label">Nome:</span>
                            <span>${compra.clienteNome}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Data do Pedido:</span>
                            <span>${compra.dataCompra} às ${compra.horaCompra}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Tipo:</span>
                            <span>${getTipoTransacaoLabel(compra.tipoTransacao)}</span>
                        </div>
                    </div>

                    <div class="produto-destaque">
                        <div class="produto-nome">${compra.produto}</div>
                        <div style="margin: 10px 0;">
                            <strong>Material:</strong> ${getMaterialLabel(compra.material)} |
                            <strong>Quantidade:</strong> ${compra.quantidade}
                        </div>
                        <div class="produto-valor">${compra.valorTotalFormatado}</div>
                    </div>

                    <div class="info-grid">
                        <div class="section">
                            <h3>💳 Pagamento</h3>
                            <div class="info-item">
                                <span class="label">Forma:</span>
                                <span>${getFormaPagamentoLabel(compra.formaPagamento)}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Status:</span>
                                <span>${getStatusPagamentoLabel(compra.statusPagamento)}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Valor Total:</span>
                                <span style="font-weight: bold; color: #28a745;">${compra.valorTotalFormatado}</span>
                            </div>
                            ${compra.statusPagamento === 'parcial' ? `
                                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 15px; border-left: 4px solid #ffc107;">
                                    <h4 style="margin: 0 0 10px 0; color: #856404;">💰 Detalhes do Pagamento Parcial</h4>
                                    <div class="info-item">
                                        <span class="label">Valor pago (60%):</span>
                                        <span style="font-weight: bold; color: #28a745;">${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.valorTotal * 0.6)}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">Saldo restante (40%):</span>
                                        <span style="font-weight: bold; color: #dc3545;">${new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(compra.valorTotal * 0.4)}</span>
                                    </div>
                                </div>
                            ` : ''}
                        </div>

                        <div class="section">
                            <h3>📦 Entrega</h3>
                            <div class="info-item">
                                <span class="label">Status:</span>
                                <span>${getStatusEntregaLabel(compra.statusEntrega)}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Previsão:</span>
                                <span>${compra.dataEntrega ? new Date(compra.dataEntrega).toLocaleDateString('pt-BR') : 'A definir'}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Prazo:</span>
                                <span>15 a 20 dias úteis</span>
                            </div>
                        </div>
                    </div>

                    ${compra.observacoes ? `
                        <div class="section">
                            <h3>📝 Observações</h3>
                            <p>${compra.observacoes}</p>
                        </div>
                    ` : ''}

                    <div class="section">
                        <h3>📋 Termos e Condições</h3>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>Produto 100% artesanal, pequenas variações são características naturais</li>
                            <li>Garantia de 6 meses contra defeitos de fabricação</li>
                            <li>Acompanha certificado de autenticidade do material</li>
                            <li>Em caso de dúvidas, entre em contato conosco</li>
                            <li>Guarde este documento como comprovante do seu pedido</li>
                        </ul>
                    </div>
                </div>

                <div class="footer">
                    <h3 style="margin: 0 0 10px 0;">SISOUL JOIAS</h3>
                    <p style="margin: 5px 0; font-size: 14px;">Joias que tocam a alma</p>
                    <p style="margin: 8px 0; font-size: 13px;">WhatsApp: (85) 99868-4795 | Instagram: @sisouljoias</p>
                    <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">CNPJ: 40.640.553/0001-23 | Emitido em: ${new Date().toLocaleString('pt-BR')}</p>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 30px;">
                    <button onclick="window.print()" style="background: #96815d; color: white; border: none; padding: 15px 30px; border-radius: 5px; font-size: 16px; cursor: pointer;">
                        📄 Imprimir/Salvar como PDF
                    </button>
                    <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 15px 30px; border-radius: 5px; font-size: 16px; cursor: pointer; margin-left: 10px;">
                        ❌ Fechar
                    </button>
                </div>
            </body>
            </html>
        `;

        printWindow.document.write(htmlContent);
        printWindow.document.close();

        setTimeout(() => {
            printWindow.focus();
        }, 500);

        if (window.SisoulUI) {
            SisoulUI.showToast('📄 Janela de impressão aberta! Use Ctrl+P para salvar como PDF', 'success', 5000);
        } else {
            alert('✅ Janela de impressão aberta!\n\nUse Ctrl+P ou clique em "Imprimir/Salvar como PDF" para gerar o arquivo.');
        }
    }

    // API pública do módulo
    return {
        init,
        novaCompra,
        fecharModal,
        salvarCompra,
        atualizarCompra,
        listarCompras,
        gerarGuiaPedido,
        editarCompra,
        excluirCompra,
        testarNumeracao,
        toggleCustoFields: window.toggleCustoFields
    };
})();

console.log('💰 Compras.js carregado com sucesso!');
