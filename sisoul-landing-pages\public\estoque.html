<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 Estoque - Sisoul Jo<PERSON></title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" type="image/png" href="images/logo.png">
</head>
<body>
    <div class="sisoul-container">
        <!-- Header -->
        <header class="sisoul-header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="images/logo + nome.png" alt="Sisoul Joias" class="header-logo">
                </div>
                <nav class="main-nav">
                    <a href="admin.html" class="nav-item">📊 Dashboard</a>
                    <a href="clientes.html" class="nav-item">👥 Clientes</a>
                    <a href="compras.html" class="nav-item">💰 Vendas</a>
                    <a href="orcamentos.html" class="nav-item">🧾 Orçamentos</a>
                    <a href="datas.html" class="nav-item">📅 Datas</a>
                    <a href="estoque.html" class="nav-item active">📦 Estoque</a>
                    <a href="backup.html" class="nav-item">💾 Backup</a>
                    <a href="login.html" class="nav-item logout">🚪 Sair</a>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="sisoul-main">
            <div class="page-header">
                <h1>📦 Controle de Estoque</h1>
                <p>Gerencie o estoque de materiais e acompanhe movimentações</p>
            </div>

            <!-- Status do Estoque -->
            <div class="estoque-status">
                <h3>📊 Status Atual do Estoque</h3>
                <div id="statusEstoque" class="status-grid">
                    <p>Carregando status do estoque...</p>
                </div>
            </div>

            <!-- Ações Rápidas -->
            <div class="sisoul-actions">
                <h3>Ações de Estoque</h3>
                <button class="btn success" onclick="EstoqueInterface.adicionarMaterial()">➕ Adicionar Material</button>
                <button class="btn primary" onclick="EstoqueInterface.atualizarPrecos()">💰 Atualizar Preços</button>
                <button class="btn warning" onclick="EstoqueInterface.verMovimentacoes()">📋 Ver Movimentações</button>
                <button class="btn" onclick="EstoqueInterface.atualizarStatus()">🔄 Atualizar Status</button>
            </div>

            <!-- Movimentações Recentes -->
            <div class="movimentacoes-recentes">
                <h3>📋 Movimentações Recentes</h3>
                <div id="movimentacoesRecentes">
                    <p>Carregando movimentações...</p>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="sisoul-footer">
            <div class="footer-content">
                <div class="footer-info">
                    <p>💎 Sisoul Joias - Joias que tocam a alma</p>
                    <p>📱 Instagram: @sisouljoias | 📞 WhatsApp: (85)99868-4795</p>
                    <p>🏢 CNPJ: 40.640.553/0001-23</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="js/storage.js"></script>
    <script src="js/estoque.js"></script>
    <script>
        // Interface do Estoque
        const EstoqueInterface = (function() {
            'use strict';

            // Atualizar status do estoque na interface
            function atualizarStatus() {
                if (!window.EstoqueModule) {
                    console.error('❌ Módulo de estoque não disponível');
                    return;
                }

                const status = EstoqueModule.obterStatusEstoque();
                const container = document.getElementById('statusEstoque');
                
                if (!container) return;

                let html = '<div class="status-grid">';
                
                status.forEach(item => {
                    html += `
                        <div class="status-card ${item.status.toLowerCase()}">
                            <div class="status-header">
                                <span class="status-emoji">${item.emoji}</span>
                                <h4>${item.material}</h4>
                            </div>
                            <div class="status-info">
                                <div class="quantidade">
                                    <span class="label">Quantidade:</span>
                                    <span class="value">${item.quantidade}${item.unidade}</span>
                                </div>
                                <div class="valor">
                                    <span class="label">Valor Total:</span>
                                    <span class="value">R$ ${item.valorTotal.toFixed(2)}</span>
                                </div>
                                <div class="preco">
                                    <span class="label">Preço/g:</span>
                                    <span class="value">R$ ${item.precoGrama.toFixed(2)}</span>
                                </div>
                                <div class="status">
                                    <span class="label">Status:</span>
                                    <span class="value status-${item.status.toLowerCase()}">${item.status}</span>
                                </div>
                            </div>
                            <div class="ultima-atualizacao">
                                <small>Atualizado: ${item.dataUltimaAtualizacao || 'N/A'}</small>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
                container.innerHTML = html;
            }

            // Carregar movimentações recentes
            function carregarMovimentacoes() {
                if (!window.SisoulStorage) return;

                const movimentacoes = SisoulStorage.getCollection('movimentacoes_estoque') || [];
                const recentes = movimentacoes
                    .sort((a, b) => b.timestamp - a.timestamp)
                    .slice(0, 10);

                const container = document.getElementById('movimentacoesRecentes');
                if (!container) return;

                if (recentes.length === 0) {
                    container.innerHTML = '<p>Nenhuma movimentação encontrada.</p>';
                    return;
                }

                let html = '<div class="movimentacoes-list">';
                
                recentes.forEach(mov => {
                    const tipoClass = mov.tipo === 'saida' ? 'saida' : 'entrada';
                    const tipoEmoji = mov.tipo === 'saida' ? '📤' : '📥';
                    
                    html += `
                        <div class="movimentacao-item ${tipoClass}">
                            <div class="mov-header">
                                <span class="mov-tipo">${tipoEmoji} ${mov.tipo.toUpperCase()}</span>
                                <span class="mov-data">${mov.data} ${mov.hora}</span>
                            </div>
                            <div class="mov-details">
                                <div class="mov-material">${mov.materialId === 'prata925' ? 'Prata 925' : 'Ouro 18k'}</div>
                                <div class="mov-quantidade">${mov.quantidade}g</div>
                                <div class="mov-motivo">${mov.motivo}</div>
                                ${mov.clienteNome ? `<div class="mov-cliente">Cliente: ${mov.clienteNome}</div>` : ''}
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
                container.innerHTML = html;
            }

            // Funções de ação (placeholder)
            function adicionarMaterial() {
                alert('🚧 Funcionalidade em desenvolvimento!\n\nEm breve você poderá adicionar materiais ao estoque.');
            }

            function atualizarPrecos() {
                alert('🚧 Funcionalidade em desenvolvimento!\n\nEm breve você poderá atualizar os preços dos materiais.');
            }

            function verMovimentacoes() {
                carregarMovimentacoes();
                alert('📋 Movimentações atualizadas!\n\nVeja as movimentações recentes abaixo.');
            }

            // Inicializar interface
            function init() {
                console.log('📦 Inicializando interface de estoque...');
                
                // Inicializar módulo de estoque
                if (window.EstoqueModule) {
                    EstoqueModule.init();
                }
                
                // Carregar dados
                atualizarStatus();
                carregarMovimentacoes();
                
                console.log('✅ Interface de estoque inicializada');
            }

            // API pública
            return {
                init,
                atualizarStatus,
                carregarMovimentacoes,
                adicionarMaterial,
                atualizarPrecos,
                verMovimentacoes
            };
        })();

        // Inicializar quando DOM estiver pronto
        document.addEventListener('DOMContentLoaded', function() {
            EstoqueInterface.init();
        });
    </script>

    <style>
        .estoque-status, .movimentacoes-recentes {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .status-card {
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid;
        }

        .status-card.ok { border-left-color: #28a745; background: #f8fff9; }
        .status-card.baixo { border-left-color: #ffc107; background: #fffdf5; }
        .status-card.zerado { border-left-color: #dc3545; background: #fff5f5; }

        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .status-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .status-info .label {
            font-weight: bold;
            color: #666;
        }

        .status-info .value {
            color: #333;
        }

        .status-ok { color: #28a745; }
        .status-baixo { color: #ffc107; }
        .status-zerado { color: #dc3545; }

        .movimentacoes-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .movimentacao-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }

        .movimentacao-item.saida {
            border-left: 4px solid #dc3545;
            background: #fff5f5;
        }

        .movimentacao-item.entrada {
            border-left: 4px solid #28a745;
            background: #f8fff9;
        }

        .mov-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .mov-tipo {
            font-weight: bold;
        }

        .mov-data {
            color: #666;
            font-size: 0.9em;
        }

        .mov-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            font-size: 0.9em;
        }
    </style>
</body>
</html>
