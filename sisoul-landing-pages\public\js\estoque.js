// Módulo de Controle de Estoque Sisoul Joias
const EstoqueModule = (function() {
    'use strict';

    // Configurações do estoque
    const CONFIG = {
        alerteLimitePrata: 10, // gramas
        alerteLimiteOuro: 2,   // gramas
        precosPadrao: {
            prata: 5.50, // por grama
            ouro: 350.00 // por grama
        }
    };

    // Inicializar estoque se não existir
    function inicializarEstoque() {
        if (!window.SisoulStorage) {
            console.error('❌ Sistema de storage não disponível');
            return;
        }

        const estoque = SisoulStorage.getCollection('estoque') || [];
        
        if (estoque.length === 0) {
            // Criar estoque inicial
            const estoqueInicial = [
                {
                    id: 'prata925',
                    material: 'Prata 925',
                    quantidade: 100, // gramas
                    unidade: 'g',
                    precoGrama: CONFIG.precosPadrao.prata,
                    valorTotal: 100 * CONFIG.precosPadrao.prata,
                    dataUltimaAtualizacao: new Date().toLocaleDateString('pt-BR'),
                    alerteLimite: CONFIG.alerteLimitePrata
                },
                {
                    id: 'ouro18k',
                    material: 'Ouro 18k',
                    quantidade: 20, // gramas
                    unidade: 'g',
                    precoGrama: CONFIG.precosPadrao.ouro,
                    valorTotal: 20 * CONFIG.precosPadrao.ouro,
                    dataUltimaAtualizacao: new Date().toLocaleDateString('pt-BR'),
                    alerteLimite: CONFIG.alerteLimiteOuro
                }
            ];

            estoqueInicial.forEach(item => {
                SisoulStorage.saveItem('estoque', item);
            });

            console.log('✅ Estoque inicial criado');
        }
    }

    // Obter item do estoque
    function obterItemEstoque(materialId) {
        if (!window.SisoulStorage) return null;
        return SisoulStorage.getItem('estoque', materialId);
    }

    // Atualizar quantidade no estoque
    function atualizarEstoque(materialId, quantidadeUsada, operacao = 'subtrair') {
        try {
            const item = obterItemEstoque(materialId);
            if (!item) {
                console.error(`❌ Material ${materialId} não encontrado no estoque`);
                return false;
            }

            const quantidadeAnterior = item.quantidade;
            
            if (operacao === 'subtrair') {
                item.quantidade -= quantidadeUsada;
            } else if (operacao === 'adicionar') {
                item.quantidade += quantidadeUsada;
            }

            // Atualizar valor total
            item.valorTotal = item.quantidade * item.precoGrama;
            item.dataUltimaAtualizacao = new Date().toLocaleDateString('pt-BR');
            item.horaUltimaAtualizacao = new Date().toLocaleTimeString('pt-BR');

            // Salvar no storage
            SisoulStorage.saveItem('estoque', item);

            // Log da operação
            console.log(`📦 Estoque atualizado - ${item.material}:`, {
                anterior: quantidadeAnterior,
                usado: quantidadeUsada,
                atual: item.quantidade,
                operacao: operacao
            });

            // Verificar alertas
            verificarAlertas(item);

            return true;

        } catch (error) {
            console.error('❌ Erro ao atualizar estoque:', error);
            return false;
        }
    }

    // Verificar alertas de estoque baixo
    function verificarAlertas(item) {
        if (item.quantidade <= item.alerteLimite) {
            const status = item.quantidade <= 0 ? 'ZERADO' : 'BAIXO';
            const emoji = item.quantidade <= 0 ? '🚨' : '⚠️';
            
            console.warn(`${emoji} ALERTA DE ESTOQUE ${status}:`, {
                material: item.material,
                quantidade: item.quantidade,
                limite: item.alerteLimite
            });

            // Mostrar alerta visual se estoque zerado
            if (item.quantidade <= 0) {
                setTimeout(() => {
                    alert(`🚨 ESTOQUE ZERADO!\n\n` +
                          `Material: ${item.material}\n` +
                          `Quantidade atual: ${item.quantidade}g\n` +
                          `Status: ESTOQUE NEGATIVO\n\n` +
                          `⚠️ Reponha o estoque o quanto antes!`);
                }, 1000);
            }
        }
    }

    // Processar venda e deduzir do estoque
    function processarVenda(venda) {
        try {
            console.log('📦 Processando venda para estoque:', venda);

            // Extrair informações de material da venda
            const materialInfo = extrairMaterialDaVenda(venda);
            
            if (!materialInfo) {
                console.log('ℹ️ Venda sem informações de material para estoque');
                return true;
            }

            const { materialId, quantidade } = materialInfo;
            
            // Atualizar estoque
            const sucesso = atualizarEstoque(materialId, quantidade, 'subtrair');
            
            if (sucesso) {
                // Registrar movimentação
                registrarMovimentacao({
                    tipo: 'saida',
                    materialId: materialId,
                    quantidade: quantidade,
                    motivo: 'Venda',
                    vendaId: venda.id,
                    clienteNome: venda.clienteNome,
                    data: new Date().toLocaleDateString('pt-BR'),
                    hora: new Date().toLocaleTimeString('pt-BR')
                });

                console.log('✅ Estoque atualizado para venda:', venda.id);
            }

            return sucesso;

        } catch (error) {
            console.error('❌ Erro ao processar venda no estoque:', error);
            return false;
        }
    }

    // Extrair material da descrição da venda
    function extrairMaterialDaVenda(venda) {
        const descricao = (venda.produtoDescricao || '').toLowerCase();
        
        // Detectar material baseado na descrição
        if (descricao.includes('prata') || descricao.includes('925')) {
            return {
                materialId: 'prata925',
                quantidade: estimarQuantidadePrata(venda)
            };
        }
        
        if (descricao.includes('ouro') || descricao.includes('18k')) {
            return {
                materialId: 'ouro18k',
                quantidade: estimarQuantidadeOuro(venda)
            };
        }

        return null;
    }

    // Estimar quantidade de prata baseada no valor
    function estimarQuantidadePrata(venda) {
        const valor = parseFloat(venda.valorTotal) || 0;
        // Estimativa: 30% do valor é material, resto é mão de obra
        const valorMaterial = valor * 0.3;
        const precoPrata = CONFIG.precosPadrao.prata;
        return Math.round((valorMaterial / precoPrata) * 100) / 100; // 2 casas decimais
    }

    // Estimar quantidade de ouro baseada no valor
    function estimarQuantidadeOuro(venda) {
        const valor = parseFloat(venda.valorTotal) || 0;
        // Estimativa: 40% do valor é material para ouro
        const valorMaterial = valor * 0.4;
        const precoOuro = CONFIG.precosPadrao.ouro;
        return Math.round((valorMaterial / precoOuro) * 100) / 100; // 2 casas decimais
    }

    // Registrar movimentação de estoque
    function registrarMovimentacao(movimentacao) {
        try {
            const mov = {
                id: Date.now().toString(),
                ...movimentacao,
                timestamp: Date.now()
            };

            SisoulStorage.saveItem('movimentacoes_estoque', mov);
            console.log('📝 Movimentação registrada:', mov);

        } catch (error) {
            console.error('❌ Erro ao registrar movimentação:', error);
        }
    }

    // Obter status do estoque
    function obterStatusEstoque() {
        const estoque = SisoulStorage.getCollection('estoque') || [];
        
        return estoque.map(item => ({
            ...item,
            status: item.quantidade <= 0 ? 'ZERADO' : 
                   item.quantidade <= item.alerteLimite ? 'BAIXO' : 'OK',
            emoji: item.quantidade <= 0 ? '🚨' : 
                  item.quantidade <= item.alerteLimite ? '⚠️' : '✅'
        }));
    }

    // Inicializar módulo
    function init() {
        console.log('📦 Inicializando módulo de Estoque...');
        inicializarEstoque();
        console.log('✅ Módulo de Estoque inicializado');
    }

    // API pública
    return {
        init,
        processarVenda,
        atualizarEstoque,
        obterItemEstoque,
        obterStatusEstoque,
        verificarAlertas,
        registrarMovimentacao
    };
})();

console.log('📦 Estoque.js carregado com sucesso!');
