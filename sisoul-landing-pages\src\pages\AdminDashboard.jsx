import { useState, useEffect } from 'react'
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom'
import ClientManager from '../components/admin/ClientManager'
import PurchaseManager from '../components/admin/PurchaseManager'
import SpecialDatesManager from '../components/admin/SpecialDatesManager'
import Dashboard from '../components/admin/Dashboard'
import TestimonialsManager from '../components/TestimonialsManager'
import { useSaveManager } from '../hooks/useSaveManager'
import UnsavedChangesWarning from '../components/admin/UnsavedChangesWarning'

const AdminDashboard = ({ onLogout }) => {
  const navigate = useNavigate()
  const location = useLocation()

  // Estados para dados
  const [clients, setClients] = useState([])
  const [purchases, setPurchases] = useState([])
  const [specialDates, setSpecialDates] = useState([])
  const [testimonials, setTestimonials] = useState([])
  const [catalogLeads, setCatalogLeads] = useState([])
  const [dataLoaded, setDataLoaded] = useState(false)

  // Sistema de salvamento manual
  const saveManager = useSaveManager()

  // Determinar aba ativa baseada na URL
  const getActiveTab = () => {
    const path = location.pathname
    if (path.includes('/clientes')) return 'clients'
    if (path.includes('/compras')) return 'purchases'
    if (path.includes('/datas')) return 'dates'
    if (path.includes('/depoimentos')) return 'testimonials'
    if (path.includes('/catalogos')) return 'catalogs'
    if (path.includes('/backup')) return 'backup'
    return 'dashboard'
  }

  const activeTab = getActiveTab()

  // Carregar dados do localStorage
  useEffect(() => {
    try {
      const savedClients = localStorage.getItem('sisoul_clients')
      const savedPurchases = localStorage.getItem('sisoul_purchases')
      const savedSpecialDates = localStorage.getItem('sisoul_special_dates')
      const savedCatalogLeads = localStorage.getItem('sisoul_catalog_leads')
      const savedTestimonials = localStorage.getItem('sisoul_testimonials')

      if (savedClients) {
        const clientsData = JSON.parse(savedClients)
        setClients(Array.isArray(clientsData) ? clientsData : [])
      }
      if (savedPurchases) {
        const purchasesData = JSON.parse(savedPurchases)
        setPurchases(Array.isArray(purchasesData) ? purchasesData : [])
      }
      if (savedSpecialDates) {
        const datesData = JSON.parse(savedSpecialDates)
        setSpecialDates(Array.isArray(datesData) ? datesData : [])
      }
      if (savedCatalogLeads) {
        const leadsData = JSON.parse(savedCatalogLeads)
        setCatalogLeads(Array.isArray(leadsData) ? leadsData : [])
      }
      if (savedTestimonials) {
        const testimonialsData = JSON.parse(savedTestimonials)
        setTestimonials(Array.isArray(testimonialsData) ? testimonialsData : [])
      }

      setDataLoaded(true)
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      setDataLoaded(true)
    }
  }, [])

  // Função para salvar todos os dados
  const saveAllData = async () => {
    try {
      localStorage.setItem('sisoul_clients', JSON.stringify(clients))
      localStorage.setItem('sisoul_purchases', JSON.stringify(purchases))
      localStorage.setItem('sisoul_special_dates', JSON.stringify(specialDates))
      localStorage.setItem('sisoul_catalog_leads', JSON.stringify(catalogLeads))
      localStorage.setItem('sisoul_testimonials', JSON.stringify(testimonials))

      // Criar backup automático também
      const backupData = {
        clients,
        purchases,
        specialDates,
        catalogLeads,
        testimonials,
        timestamp: new Date().toISOString()
      }
      localStorage.setItem('sisoul_backup_auto', JSON.stringify(backupData))

      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  }

  // Marcar como alterado quando dados mudarem
  useEffect(() => {
    if (dataLoaded) {
      saveManager.markAsChanged()
    }
  }, [clients, purchases, specialDates, catalogLeads, testimonials, dataLoaded, saveManager])

  // Backup automático a cada 1 hora
  useEffect(() => {
    const interval = setInterval(() => {
      if (dataLoaded) {
        const backupData = {
          clients,
          purchases,
          specialDates,
          catalogLeads,
          testimonials,
          timestamp: new Date().toISOString()
        }
        localStorage.setItem('sisoul_backup_auto', JSON.stringify(backupData))
        console.log('Backup automático realizado:', new Date().toLocaleTimeString())
      }
    }, 3600000) // 1 hora

    return () => clearInterval(interval)
  }, [clients, purchases, specialDates, catalogLeads, testimonials, dataLoaded])

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊', path: '/admin' },
    { id: 'clients', name: 'Clientes', icon: '👥', path: '/admin/clientes' },
    { id: 'purchases', name: 'Compras', icon: '🛍️', path: '/admin/compras' },
    { id: 'dates', name: 'Datas Especiais', icon: '📅', path: '/admin/datas' },
    { id: 'testimonials', name: 'Depoimentos', icon: '💬', path: '/admin/depoimentos' },
    { id: 'catalogs', name: 'Leads Catálogos', icon: '📖', path: '/admin/catalogos' },
    { id: 'backup', name: 'Backup', icon: '🛡️', path: '/admin/backup' }
  ]

  return (
    <div className="min-h-screen" style={{backgroundColor: '#e2e2e2'}}>
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                Painel Administrativo - Sistema Completo
              </span>
              {saveManager.lastSaved && (
                <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full ml-2">
                  ✅ Salvo {saveManager.lastSaved.toLocaleTimeString('pt-BR')}
                </span>
              )}
              {saveManager.hasUnsavedChanges && (
                <span className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full ml-2">
                  ⚠️ Alterações não salvas
                </span>
              )}
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right text-xs text-gray-500">
                📊 {clients.length} clientes | 🛍️ {purchases.length} compras | 📅 {specialDates.length} datas
              </div>

              {/* Botão de Salvamento */}
              <button
                onClick={() => saveManager.saveData(saveAllData)}
                disabled={!saveManager.hasUnsavedChanges || saveManager.isSaving}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  saveManager.hasUnsavedChanges && !saveManager.isSaving
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {saveManager.isSaving ? '💾 Salvando...' : '💾 Salvar Dados'}
              </button>

              <button
                onClick={onLogout}
                className="flex items-center text-gray-600 hover:text-sisoul-gold transition-colors"
              >
                <span className="mr-2">Sair</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <div className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => navigate(tab.path)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-sisoul-gold text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <span className="text-lg mr-3">{tab.icon}</span>
                  <span className="font-medium">{tab.name}</span>
                </button>
              ))}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <Routes>
            <Route path="/" element={
              <Dashboard
                clients={clients}
                purchases={purchases}
                specialDates={specialDates}
                catalogLeads={catalogLeads}
                testimonials={testimonials}
              />
            } />
            <Route path="/clientes" element={
              <ClientManager
                clients={clients}
                setClients={setClients}
                purchases={purchases}
                specialDates={specialDates}
              />
            } />
            <Route path="/compras" element={
              <PurchaseManager
                purchases={purchases}
                setPurchases={setPurchases}
                clients={clients}
              />
            } />
            <Route path="/datas" element={
              <SpecialDatesManager
                specialDates={specialDates}
                setSpecialDates={setSpecialDates}
                clients={clients}
              />
            } />
            <Route path="/depoimentos" element={
              <TestimonialsManager
                testimonials={testimonials}
                setTestimonials={setTestimonials}
              />
            } />
          </Routes>
        </main>
      </div>

      {/* Aviso de alterações não salvas */}
      <UnsavedChangesWarning hasUnsavedChanges={saveManager.hasUnsavedChanges} />
    </div>
  )
}



export default AdminDashboard
